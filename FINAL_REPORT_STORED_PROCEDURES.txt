﻿DANH SÁCH CÁC FILE .CS SỬ DỤNG STORED PROCEDURE TRONG TINYCRM
================================================================

I. CÁC FOLDER ĐÃ TÌM KIẾM:
================================================================
1. TinyCRM\Access            
2. TinyCRM\Appeal            
3. TinyCRM\AutomaticTask     
4. TinyCRM\Behavior          
5. TinyCRM\bin               
6. TinyCRM\Building          
7. TinyCRM\BuiltInCommand    
8. TinyCRM\BusinessPermission
9. TinyCRM\BusinessResult    
10. TinyCRM\Callback          
11. TinyCRM\Campaign          
12. TinyCRM\Channel           
13. TinyCRM\ConfigureTicket   
14. TinyCRM\content           
15. TinyCRM\ContentTemplate   
16. TinyCRM\Customer          
17. TinyCRM\CustomerContext   
18. TinyCRM\CustomerVersion...
19. TinyCRM\Dashboard         
20. TinyCRM\DigitalCampaign   
21. TinyCRM\DigitalChannel    
22. TinyCRM\DigitalChannelM...
23. TinyCRM\DigitalContact    
24. TinyCRM\DigitalMessage    
25. TinyCRM\DigitalMessageLog 
26. TinyCRM\DigitalMessageT...
27. TinyCRM\DigitalPushCode   
28. TinyCRM\DynamicDefinedT...
29. TinyCRM\DynamicForm       
30. TinyCRM\DynamicTable      
31. TinyCRM\ECommerce         
32. TinyCRM\Endorsement       
33. TinyCRM\EntityLink        
34. TinyCRM\Enums             
35. TinyCRM\EquipmentSystem   
36. TinyCRM\ExpenseItem       
37. TinyCRM\ExternalApiEndp...
38. TinyCRM\FeeCategory       
39. TinyCRM\FeeCategoryPart   
40. TinyCRM\FinishRequestTi...
41. TinyCRM\GanttProject      
42. TinyCRM\Geolocation       
43. TinyCRM\GridDynamicField  
44. TinyCRM\HubEvents         
45. TinyCRM\Image             
46. TinyCRM\Import            
47. TinyCRM\ImportB2BCustom...
48. TinyCRM\ImportCustomerRaw 
49. TinyCRM\ImportCustomerS...
50. TinyCRM\ImportRequestTi...
51. TinyCRM\ImportSOSession   
52. TinyCRM\ImportTask        
53. TinyCRM\InfoList          
54. TinyCRM\KnowledgeBase     
55. TinyCRM\Mail              
56. TinyCRM\Maintenance       
57. TinyCRM\MobileNotification
58. TinyCRM\MonthlyPartFee    
59. TinyCRM\MonthlyPartFeeB...
60. TinyCRM\Notification      
61. TinyCRM\NotificationCase  
62. TinyCRM\NotificationCha...
63. TinyCRM\obj               
64. TinyCRM\Order             
65. TinyCRM\Organization      
66. TinyCRM\Outbound          
67. TinyCRM\Part              
68. TinyCRM\PartBooking       
69. TinyCRM\PartCustomer      
70. TinyCRM\PartRepairing     
71. TinyCRM\PartServiceUsed...
72. TinyCRM\PaymentRequest    
73. TinyCRM\Phase             
74. TinyCRM\PlanJob           
75. TinyCRM\Product           
76. TinyCRM\Properties        
77. TinyCRM\Query             
78. TinyCRM\Report            
79. TinyCRM\RequestTicket     
80. TinyCRM\RequestTicketDy...
81. TinyCRM\ResultCode        
82. TinyCRM\Retrieval         
83. TinyCRM\ScheduledTaskCo...
84. TinyCRM\SelfServiceFlow   
85. TinyCRM\ServiceCategory   
86. TinyCRM\ServiceType       
87. TinyCRM\Sms               
88. TinyCRM\SMSLog            
89. TinyCRM\Survey            
90. TinyCRM\TaskType          
91. TinyCRM\Tax               
92. TinyCRM\TbCallback        
93. TinyCRM\TemplateLibrary   
94. TinyCRM\Ticket            
95. TinyCRM\TicketHotButton   
96. TinyCRM\TripRoute         
97. TinyCRM\User              
98. TinyCRM\UserAccount       
99. TinyCRM\UserTaskAssignm...
100. TinyCRM\Utilities         
101. TinyCRM\Utility           
102. TinyCRM\VirtualOffice     
103. TinyCRM\WebChat           
104. TinyCRM\Workflow          
105. TinyCRM\WorkflowTaskTyp...
106. TinyCRM\WorkflowTaskTyp...
107. TinyCRM\WorkItemSummary   
108. TinyCRM\Appeal\Queries    
109. TinyCRM\AutomaticTask\C...
110. TinyCRM\AutomaticTask\E...
111. TinyCRM\AutomaticTask\E...
112. TinyCRM\AutomaticTask\M...
113. TinyCRM\AutomaticTask\Q...
114. TinyCRM\Behavior\Commands 
115. TinyCRM\Behavior\Queries  
116. TinyCRM\bin\Debug         
117. TinyCRM\bin\Debug\net9.0  
118. TinyCRM\bin\Debug\net9....
119. TinyCRM\Building\Commands 
120. TinyCRM\Building\Queries  
121. TinyCRM\BusinessPermiss...
122. TinyCRM\BusinessPermiss...
123. TinyCRM\BusinessResult\...
124. TinyCRM\BusinessResult\...
125. TinyCRM\BusinessResult\...
126. TinyCRM\Callback\Commands 
127. TinyCRM\Callback\Queries  
128. TinyCRM\Campaign\Commands 
129. TinyCRM\Campaign\Queries  
130. TinyCRM\Campaign\Tasks    
131. TinyCRM\Channel\Commands  
132. TinyCRM\Channel\Queries   
133. TinyCRM\content\net40     
134. TinyCRM\ContentTemplate...
135. TinyCRM\ContentTemplate...
136. TinyCRM\Customer\Commands 
137. TinyCRM\Customer\Events   
138. TinyCRM\Customer\Queries  
139. TinyCRM\Customer\Tasks    
140. TinyCRM\CustomerContext...
141. TinyCRM\CustomerContext...
142. TinyCRM\CustomerContext...
143. TinyCRM\CustomerVersion...
144. TinyCRM\CustomerVersion...
145. TinyCRM\Dashboard\Bucket  
146. TinyCRM\DigitalCampaign...
147. TinyCRM\DigitalChannel\...
148. TinyCRM\DigitalChannel\...
149. TinyCRM\DigitalChannelM...
150. TinyCRM\DigitalChannelM...
151. TinyCRM\DigitalContact\...
152. TinyCRM\DigitalContact\...
153. TinyCRM\DigitalMessage\...
154. TinyCRM\DigitalMessage\...
155. TinyCRM\DigitalMessage\...
156. TinyCRM\DigitalMessage\...
157. TinyCRM\DigitalMessageL...
158. TinyCRM\DigitalMessageL...
159. TinyCRM\DigitalMessageT...
160. TinyCRM\DigitalMessageT...
161. TinyCRM\DigitalPushCode...
162. TinyCRM\DigitalPushCode...
163. TinyCRM\DynamicDefinedT...
164. TinyCRM\DynamicDefinedT...
165. TinyCRM\DynamicForm\Com...
166. TinyCRM\DynamicForm\Que...
167. TinyCRM\ECommerce\Commands
168. TinyCRM\Endorsement\Com...
169. TinyCRM\Endorsement\Events
170. TinyCRM\Endorsement\Que...
171. TinyCRM\EntityLink\Comm...
172. TinyCRM\EntityLink\Queries
173. TinyCRM\EquipmentSystem...
174. TinyCRM\EquipmentSystem...
175. TinyCRM\ExpenseItem\Com...
176. TinyCRM\ExpenseItem\Que...
177. TinyCRM\ExternalApiEndp...
178. TinyCRM\ExternalApiEndp...
179. TinyCRM\FeeCategory\Com...
180. TinyCRM\FeeCategory\Que...
181. TinyCRM\FeeCategoryPart...
182. TinyCRM\FeeCategoryPart...
183. TinyCRM\FinishRequestTi...
184. TinyCRM\FinishRequestTi...
185. TinyCRM\GanttProject\Co...
186. TinyCRM\GanttProject\Qu...
187. TinyCRM\Geolocation\Que...
188. TinyCRM\GridDynamicFiel...
189. TinyCRM\GridDynamicFiel...
190. TinyCRM\GridDynamicFiel...
191. TinyCRM\GridDynamicFiel...
192. TinyCRM\GridDynamicFiel...
193. TinyCRM\GridDynamicFiel...
194. TinyCRM\GridDynamicFiel...
195. TinyCRM\GridDynamicFiel...
196. TinyCRM\GridDynamicFiel...
197. TinyCRM\GridDynamicFiel...
198. TinyCRM\GridDynamicFiel...
199. TinyCRM\GridDynamicFiel...
200. TinyCRM\Image\Commands    
201. TinyCRM\Image\Queries     
202. TinyCRM\ImportB2BCustom...
203. TinyCRM\ImportB2BCustom...
204. TinyCRM\ImportCustomerR...
205. TinyCRM\ImportCustomerS...
206. TinyCRM\ImportRequestTi...
207. TinyCRM\ImportRequestTi...
208. TinyCRM\ImportRequestTi...
209. TinyCRM\ImportSOSession...
210. TinyCRM\ImportSOSession...
211. TinyCRM\ImportTask\Comm...
212. TinyCRM\ImportTask\Queries
213. TinyCRM\ImportTask\Tasks  
214. TinyCRM\InfoList\Commands 
215. TinyCRM\InfoList\Queries  
216. TinyCRM\KnowledgeBase\C...
217. TinyCRM\KnowledgeBase\Q...
218. TinyCRM\Mail\Commands     
219. TinyCRM\Mail\Events       
220. TinyCRM\Mail\Queries      
221. TinyCRM\Maintenance\Com...
222. TinyCRM\Maintenance\Que...
223. TinyCRM\MobileNotificat...
224. TinyCRM\MobileNotificat...
225. TinyCRM\MobileNotificat...
226. TinyCRM\MonthlyPartFee\...
227. TinyCRM\MonthlyPartFee\...
228. TinyCRM\MonthlyPartFee\...
229. TinyCRM\MonthlyPartFee\...
230. TinyCRM\MonthlyPartFee\...
231. TinyCRM\MonthlyPartFeeB...
232. TinyCRM\MonthlyPartFeeB...
233. TinyCRM\MonthlyPartFeeB...
234. TinyCRM\Notification\Ev...
235. TinyCRM\Notification\Tasks
236. TinyCRM\NotificationCas...
237. TinyCRM\NotificationCas...
238. TinyCRM\NotificationCas...
239. TinyCRM\NotificationCas...
240. TinyCRM\NotificationCas...
241. TinyCRM\NotificationCas...
242. TinyCRM\NotificationCas...
243. TinyCRM\NotificationCas...
244. TinyCRM\NotificationCha...
245. TinyCRM\NotificationCha...
246. TinyCRM\obj\Debug         
247. TinyCRM\obj\Debug\net9.0  
248. TinyCRM\obj\Debug\net9....
249. TinyCRM\obj\Debug\net9....
250. TinyCRM\Order\Commands    
251. TinyCRM\Order\Queries     
252. TinyCRM\Order\Commands\...
253. TinyCRM\Order\Commands\...
254. TinyCRM\Order\Commands\...
255. TinyCRM\Organization\Qu...
256. TinyCRM\Outbound\Additi...
257. TinyCRM\Outbound\AgentF...
258. TinyCRM\Outbound\Appoin...
259. TinyCRM\Outbound\Appoin...
260. TinyCRM\Outbound\Appoin...
261. TinyCRM\Outbound\Brand    
262. TinyCRM\Outbound\CallRe...
263. TinyCRM\Outbound\Campaign 
264. TinyCRM\Outbound\Campai...
265. TinyCRM\Outbound\Campai...
266. TinyCRM\Outbound\Company  
267. TinyCRM\Outbound\Comple...
268. TinyCRM\Outbound\Contact  
269. TinyCRM\Outbound\Contac...
270. TinyCRM\Outbound\Contac...
271. TinyCRM\Outbound\Contac...
272. TinyCRM\Outbound\Contract 
273. TinyCRM\Outbound\District 
274. TinyCRM\Outbound\Dynami...
275. TinyCRM\Outbound\Endors...
276. TinyCRM\Outbound\ExcelF...
277. TinyCRM\Outbound\ExcelI...
278. TinyCRM\Outbound\Expens...
279. TinyCRM\Outbound\HotLis...
280. TinyCRM\Outbound\HotLis...
281. TinyCRM\Outbound\HotLis...
282. TinyCRM\Outbound\Import...
283. TinyCRM\Outbound\Lead     
284. TinyCRM\Outbound\LeadAs...
285. TinyCRM\Outbound\Organi...
286. TinyCRM\Outbound\packages 
287. TinyCRM\Outbound\Parent...
288. TinyCRM\Outbound\Paymen...
289. TinyCRM\Outbound\Product  
290. TinyCRM\Outbound\Prospect 
291. TinyCRM\Outbound\Prospe...
292. TinyCRM\Outbound\Province 
293. TinyCRM\Outbound\Region   
294. TinyCRM\Outbound\Reminder 
295. TinyCRM\Outbound\Report   
296. TinyCRM\Outbound\Report...
297. TinyCRM\Outbound\Role     
298. TinyCRM\Outbound\Sale     
299. TinyCRM\Outbound\SlotTime 
300. TinyCRM\Outbound\Stagin...
301. TinyCRM\Outbound\SubCon...
302. TinyCRM\Outbound\SysCon...
303. TinyCRM\Outbound\Team     
304. TinyCRM\Outbound\TeamLe...
305. TinyCRM\Outbound\Templa...
306. TinyCRM\Outbound\UserAc...
307. TinyCRM\Outbound\UserCh...
308. TinyCRM\Outbound\Utilities
309. TinyCRM\Outbound\VNMCon...
310. TinyCRM\Outbound\Ward     
311. TinyCRM\Outbound\Additi...
312. TinyCRM\Outbound\Additi...
313. TinyCRM\Outbound\AgentF...
314. TinyCRM\Outbound\Appoin...
315. TinyCRM\Outbound\Appoin...
316. TinyCRM\Outbound\Appoin...
317. TinyCRM\Outbound\Appoin...
318. TinyCRM\Outbound\Appoin...
319. TinyCRM\Outbound\Brand\...
320. TinyCRM\Outbound\Brand\...
321. TinyCRM\Outbound\Brand\...
322. TinyCRM\Outbound\CallRe...
323. TinyCRM\Outbound\CallRe...
324. TinyCRM\Outbound\Campai...
325. TinyCRM\Outbound\Campai...
326. TinyCRM\Outbound\Campai...
327. TinyCRM\Outbound\Campai...
328. TinyCRM\Outbound\Campai...
329. TinyCRM\Outbound\Campai...
330. TinyCRM\Outbound\Campai...
331. TinyCRM\Outbound\Campai...
332. TinyCRM\Outbound\Campai...
333. TinyCRM\Outbound\Compan...
334. TinyCRM\Outbound\Compan...
335. TinyCRM\Outbound\Compan...
336. TinyCRM\Outbound\Comple...
337. TinyCRM\Outbound\Comple...
338. TinyCRM\Outbound\Comple...
339. TinyCRM\Outbound\Contac...
340. TinyCRM\Outbound\Contac...
341. TinyCRM\Outbound\Contac...
342. TinyCRM\Outbound\Contac...
343. TinyCRM\Outbound\Contac...
344. TinyCRM\Outbound\Contac...
345. TinyCRM\Outbound\Contac...
346. TinyCRM\Outbound\Contac...
347. TinyCRM\Outbound\Contac...
348. TinyCRM\Outbound\Contac...
349. TinyCRM\Outbound\Contra...
350. TinyCRM\Outbound\Distri...
351. TinyCRM\Outbound\Distri...
352. TinyCRM\Outbound\Distri...
353. TinyCRM\Outbound\ExcelF...
354. TinyCRM\Outbound\ExcelI...
355. TinyCRM\Outbound\ExcelI...
356. TinyCRM\Outbound\HotLis...
357. TinyCRM\Outbound\HotLis...
358. TinyCRM\Outbound\HotLis...
359. TinyCRM\Outbound\HotLis...
360. TinyCRM\Outbound\Import...
361. TinyCRM\Outbound\Import...
362. TinyCRM\Outbound\Import...
363. TinyCRM\Outbound\Lead\C...
364. TinyCRM\Outbound\Lead\Q...
365. TinyCRM\Outbound\LeadAs...
366. TinyCRM\Outbound\LeadAs...
367. TinyCRM\Outbound\Organi...
368. TinyCRM\Outbound\packag...
369. TinyCRM\Outbound\packag...
370. TinyCRM\Outbound\packag...
371. TinyCRM\Outbound\packag...
372. TinyCRM\Outbound\packag...
373. TinyCRM\Outbound\packag...
374. TinyCRM\Outbound\packag...
375. TinyCRM\Outbound\packag...
376. TinyCRM\Outbound\packag...
377. TinyCRM\Outbound\packag...
378. TinyCRM\Outbound\packag...
379. TinyCRM\Outbound\packag...
380. TinyCRM\Outbound\packag...
381. TinyCRM\Outbound\packag...
382. TinyCRM\Outbound\packag...
383. TinyCRM\Outbound\packag...
384. TinyCRM\Outbound\packag...
385. TinyCRM\Outbound\packag...
386. TinyCRM\Outbound\packag...
387. TinyCRM\Outbound\packag...
388. TinyCRM\Outbound\packag...
389. TinyCRM\Outbound\packag...
390. TinyCRM\Outbound\Parent...
391. TinyCRM\Outbound\Parent...
392. TinyCRM\Outbound\Paymen...
393. TinyCRM\Outbound\Produc...
394. TinyCRM\Outbound\Produc...
395. TinyCRM\Outbound\Produc...
396. TinyCRM\Outbound\Prospe...
397. TinyCRM\Outbound\Prospe...
398. TinyCRM\Outbound\Prospe...
399. TinyCRM\Outbound\Prospe...
400. TinyCRM\Outbound\Prospe...
401. TinyCRM\Outbound\Prospe...
402. TinyCRM\Outbound\Provin...
403. TinyCRM\Outbound\Provin...
404. TinyCRM\Outbound\Provin...
405. TinyCRM\Outbound\Region...
406. TinyCRM\Outbound\Region...
407. TinyCRM\Outbound\Region...
408. TinyCRM\Outbound\Remind...
409. TinyCRM\Outbound\Remind...
410. TinyCRM\Outbound\Remind...
411. TinyCRM\Outbound\Report...
412. TinyCRM\Outbound\Report...
413. TinyCRM\Outbound\Sale\C...
414. TinyCRM\Outbound\Sale\Q...
415. TinyCRM\Outbound\Stagin...
416. TinyCRM\Outbound\Stagin...
417. TinyCRM\Outbound\Stagin...
418. TinyCRM\Outbound\SubCon...
419. TinyCRM\Outbound\SubCon...
420. TinyCRM\Outbound\SubCon...
421. TinyCRM\Outbound\SysCon...
422. TinyCRM\Outbound\Team\C...
423. TinyCRM\Outbound\Team\Q...
424. TinyCRM\Outbound\TeamLe...
425. TinyCRM\Outbound\Templa...
426. TinyCRM\Outbound\UserCh...
427. TinyCRM\Outbound\UserCh...
428. TinyCRM\Outbound\VNMCon...
429. TinyCRM\Outbound\VNMCon...
430. TinyCRM\Outbound\Ward\C...
431. TinyCRM\Outbound\Ward\E...
432. TinyCRM\Outbound\Ward\Q...
433. TinyCRM\Part\Commands     
434. TinyCRM\Part\Queries      
435. TinyCRM\PartBooking\Com...
436. TinyCRM\PartBooking\Que...
437. TinyCRM\PartCustomer\Co...
438. TinyCRM\PartCustomer\Qu...
439. TinyCRM\PartRepairing\C...
440. TinyCRM\PartRepairing\Q...
441. TinyCRM\PartServiceUsed...
442. TinyCRM\PartServiceUsed...
443. TinyCRM\PaymentRequest\...
444. TinyCRM\PaymentRequest\...
445. TinyCRM\Phase\Command     
446. TinyCRM\Phase\EventHand...
447. TinyCRM\Phase\Events      
448. TinyCRM\Phase\Notificat...
449. TinyCRM\Phase\Queries     
450. TinyCRM\PlanJob\Commands  
451. TinyCRM\PlanJob\Queries   
452. TinyCRM\Product\Commands  
453. TinyCRM\Product\Queries   
454. TinyCRM\Properties\Data...
455. TinyCRM\Query\Command     
456. TinyCRM\Query\Queries     
457. TinyCRM\Report\Queries    
458. TinyCRM\Report\Tasks      
459. TinyCRM\Report\VenusCorp  
460. TinyCRM\RequestTicket\C...
461. TinyCRM\RequestTicket\E...
462. TinyCRM\RequestTicket\Q...
463. TinyCRM\RequestTicket\V...
464. TinyCRM\RequestTicketDy...
465. TinyCRM\RequestTicketDy...
466. TinyCRM\ResultCode\Queries
467. TinyCRM\Retrieval\Audit   
468. TinyCRM\Retrieval\Commands
469. TinyCRM\Retrieval\Queries 
470. TinyCRM\ScheduledTaskCo...
471. TinyCRM\ScheduledTaskCo...
472. TinyCRM\SelfServiceFlow...
473. TinyCRM\ServiceCategory...
474. TinyCRM\ServiceCategory...
475. TinyCRM\ServiceType\Com...
476. TinyCRM\ServiceType\Events
477. TinyCRM\ServiceType\Que...
478. TinyCRM\Sms\Commands      
479. TinyCRM\Sms\Events        
480. TinyCRM\Sms\Queries       
481. TinyCRM\Sms\Tasks         
482. TinyCRM\SMSLog\Commands   
483. TinyCRM\SMSLog\Queries    
484. TinyCRM\Survey\Commands   
485. TinyCRM\Survey\Queries    
486. TinyCRM\TaskType\AutoSe...
487. TinyCRM\TaskType\Commands 
488. TinyCRM\TaskType\Queries  
489. TinyCRM\Tax\Commands      
490. TinyCRM\Tax\Queries       
491. TinyCRM\TbCallback\Comm...
492. TinyCRM\TbCallback\Queries
493. TinyCRM\TemplateLibrary...
494. TinyCRM\Ticket\Queries    
495. TinyCRM\TicketHotButton...
496. TinyCRM\TicketHotButton...
497. TinyCRM\TripRoute\Queries 
498. TinyCRM\UserAccount\Act...
499. TinyCRM\UserAccount\Que...
500. TinyCRM\UserTaskAssignm...
501. TinyCRM\UserTaskAssignm...
502. TinyCRM\VirtualOffice\C...
503. TinyCRM\VirtualOffice\Data
504. TinyCRM\VirtualOffice\E...
505. TinyCRM\VirtualOffice\E...
506. TinyCRM\VirtualOffice\Q...
507. TinyCRM\WebChat\Commands  
508. TinyCRM\WebChat\Queries   
509. TinyCRM\Workflow\Approv...
510. TinyCRM\Workflow\Commands 
511. TinyCRM\Workflow\Queries  
512. TinyCRM\Workflow\Approv...
513. TinyCRM\WorkflowTaskTyp...
514. TinyCRM\WorkflowTaskTyp...
515. TinyCRM\WorkflowTaskTyp...
516. TinyCRM\WorkflowTaskTyp...
517. TinyCRM\WorkItemSummary...
518. TinyCRM\WorkItemSummary...

II. CÁC FILE .CS SỬ DỤNG STORED PROCEDURE:
================================================================
1. AddAnonymousDigitalContactListToCampaignCommand.cs
-------------------------------------------
- dbo.DigitalContact_AddAnonymousDigitalContactToCampaign

2. AddContactsToCampaignCommand.cs
-------------------------------------------
- telesale.Contact_AddContactsToCampaign

3. AddCustomerDigitalContactToCampaignCommand.cs
-------------------------------------------
- dbo.DigitalCampaign_AddCustomerDigitalContactToCampaign

4. AddCustomerIntoCampaignByDynamicFieldDefinitionNameCommand.cs
-------------------------------------------
- dbo.AddCustomerIntoCampaignByDynamicFieldDefinitionName

5. AddDigitalContactsToCampaignCommand.cs
-------------------------------------------
- dbo.AddDigitalContactsToCampaign

6. AddExpenseItemListToEndorsementCommand.cs
-------------------------------------------
- Error - empty file

7. AddExpenseItemsToPaymentRequestCommand.cs
-------------------------------------------
- dbo.AddExpenseItemsToPaymentRequest

8. AddUserToHotGroupCommand.cs
-------------------------------------------
- SELECT * FROM telesale.HotListGroupUser WHERE UserId = @UserId AND HotListGroupId = @GroupId
- INSERT INTO telesale.HotListGroupUser ( Id, HotListGroupId, UserId ) VALUES  ( NEWID(), @GroupId, @UserId)

9. AddWorkTicketsToCampaignCommand.cs
-------------------------------------------
- Campaign_AddWorks_AddTickets

10. AppointmentService.cs
-------------------------------------------
- telesale.GetAppointmentListReport
- telesale.NotifyAppointmentList
- telesale.SearchAppointmentsToDistribute
- telesale.SearchFieldSaleAppointmentList
- telesale.GetAppointmentTrackings
- telesale.GetAppointmentTrackingFields

11. ApproveEventContactListCommand.cs
-------------------------------------------
- cia.ApproveEventContactList

12. AutoDialScheduledTask.cs
-------------------------------------------
- dbo.GetAutoDialCustomerListInCampaign
- dbo.UpdateDialingProspectAssignment

13. AutoProcessConflictParentCommand.cs
-------------------------------------------
- VNM_ImportContact_Deduplication_AutoProcessConflictParentCommand

14. BatchCreateRequestTicketListCommand.cs
-------------------------------------------
- dbo.BatchCreateRequestTicketList

15. BatchInsertNewDynamicFieldToExistedTicketListCommand.cs
-------------------------------------------
- dbo.BatchInsertNewDynamicFieldToExistedTicketList

16. BatchRecalculateRequestTicketExecutor.cs
-------------------------------------------
- dbo.GetCustomerRequestTicketByServiceTypeId
- dbo.GetCustomerRequestTicketByServiceTypeId
- dbo.GetAllDynamicFieldValueInfoList
- dbo.GetAllDynamicDefinedTableCellValueListByRowJsonValue
- dbo.BatchUpdateDynamicDefinedTableRowJsonValueList
- DELETE dbo.BatchDynamicFieldRowJsonValueParam WHERE ImportSessionId = @ImportSessionId
- dbo.BatchUpdateDynamicFieldValueList
- DELETE dbo.BatchDynamicFieldValueParam WHERE ImportSessionId = @ImportSessionId

17. BatchUpdateSoonerProcessMinutesCommand.cs
-------------------------------------------
- dbo.BatchUpdateSoonerProcessMinutes

18. BuiltInCommandExecutor.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

19. BulkInsertCustomerRawCommand.cs
-------------------------------------------
- SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'ImportCustomerRaw'

20. CalculateAndCreateMonthlyPartFeeBatchCommand.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

21. CalculateAndCreateMonthlyPartFeeCommand.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

22. CallResultFunnelSumaryQuery.cs
-------------------------------------------
- telesale.CallResultFunnelSumary

23. CampignReportServices.cs
-------------------------------------------
- dbo.GetDetailCallTimesReport
- dbo.GetNumberOfCallByCallResultSummary

24. ChangeTaskOwnedByOrganizationIdBatchCommand.cs
-------------------------------------------
- dbo.ChangeTaskOwnedByOrganizationIdBatch

25. ChangeTicketOwnedByOrganizationIdBatchCommand.cs
-------------------------------------------
- dbo.ChangeTicketOwnedByOrganizationIdBatch

26. CheckDuplicationInInternalStagingMotherContactCommand.cs
-------------------------------------------
- VNM_ImportContact_Deduplication_CheckMotherDuplicationStatusInternal

27. CheckDynamicFieldValueDupOnTaskQuery.cs
-------------------------------------------
- dbo.CheckDynamicTableValid
- dbo.CheckDynamicFieldValueDuplicate

28. CleanUpAndBackUpNotificationScheduledTask.cs
-------------------------------------------
- dbo.CleanUpAndBackUpNotification

29. CleanupImportAppartmentsCommand.cs
-------------------------------------------
- DELETE dbo.Staging_Appartments WHERE ImportSessionId = @ImportSessionId

30. CleanupImportB2BCustomersCommand.cs
-------------------------------------------
- DELETE dbo.Staging_Customers WHERE ImportSessionId <> @ImportSessionId

31. CleanupImportFeeCategoryPartsCommand.cs
-------------------------------------------
- DELETE dbo.Staging_FeeCategoryParts WHERE ImportSessionId = @ImportSessionId

32. CleanupImportPartsCommand.cs
-------------------------------------------
- DELETE dbo.Staging_Parts WHERE ImportSessionId = @ImportSessionId

33. CleanUpImportSOCommand.cs
-------------------------------------------
- DELETE dbo.Staging_ImportSO WHERE ImportSessionId <> @ImportSessionId

34. CloseAllNotificationCaseByRootEntityCommand.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

35. CloseAllTodoTaskStatusCommand.cs
-------------------------------------------
- dbo.CloseAllTodoTaskStatus

36. CloseMobileNotificationBatchCommand.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

37. CloseNotificationCaseCommand.cs
-------------------------------------------
- dbo.CloseNotificationCases

38. CloseTaskCommand.cs
-------------------------------------------
- dbo.CloseTask

39. CommitContestExpenseCommand.cs
-------------------------------------------
- fwd.CommitContestExpense

40. ContactCallService.cs
-------------------------------------------
- telesale.GetAgentCallResultReport
- telesale.GetCallResultByTMRsReport
- telesale.GetCallResultByTMRsReport2
- telesale.GetContactCallListReport

41. ContactService.cs
-------------------------------------------
- CheckDuplicatedPhone

42. CreateEditContentTemplateListInTaskTypeCommand.cs
-------------------------------------------
- dbo.CreateEditContentTemplateListInTaskType

43. CreateEditDynamicDefinedTableCellValueListCommand.cs
-------------------------------------------
- dbo.CreateEditDynamicDefinedTableRowJsonValueList
- dbo.CreateEditDynamicDefinedTableCellValueList

44. CreateEditPartCommand.cs
-------------------------------------------
- dbo.UpdatePartFullPathName

45. CreateEditSurveyQuestionCommand.cs
-------------------------------------------
- ResetRootFlowOrders

46. CreateEditTaskTypeListInWorkflowCommand.cs
-------------------------------------------
- dbo.CreateEditTaskTypeListInWorkflow

47. CreateEditUserTaskAssignmentRoutingCommand.cs
-------------------------------------------
- dbo.UpdateUserTaskAssignmentRoutingPriority

48. CreateLogLinkDigitalContactCommand.cs
-------------------------------------------
- dbo.CreateLogLinkDigitalContact

49. CreateMonthlyPartFeeBatchCommand.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

50. CreatePartsWithTemplateCommand.cs
-------------------------------------------
- dbo.CreateRootPart

51. CreateProspectFromHotListCommand.cs
-------------------------------------------
- telesale.Contact_CreateProspectFromHotList

52. CreateSurveyFeedbackCommand.cs
-------------------------------------------
- [telesale].[CreateSurveyFeedback]

53. CreateSurveyFeedbackFromCampaignTicketAssignWorkCommand.cs
-------------------------------------------
- CreateSurveyFeedbackFromCampaignTicketAssignWork

54. CreateTicketAutomaticSurveyFeedbacksCommand.cs
-------------------------------------------
- dbo.GetDataSetFromProspectAssignmentKey

55. DataCallResultFunnelSumaryQuery.cs
-------------------------------------------
- telesale.DataCallResultFunnelSumary

56. DedupBackendAndTempCustomersCommand.cs
-------------------------------------------
- dbo.DedupBackendAndTempCustomers

57. DedupSignleBackendAndTempCustomersCommand.cs
-------------------------------------------
- dbo.DedupSignleBackendAndTempCustomers

58. DefaultUserAccountCommand.cs
-------------------------------------------
- UPDATE dbo.aspnet_Membership SET Email = @Email, LoweredEmail = LOWER(@Email) WHERE UserId = @UserId

59. DeleteContactCallCommand.cs
-------------------------------------------
- DeleteContactCall

60. DeleteCustomerRequestTicketListCommand.cs
-------------------------------------------
- dbo.DeleteCustomerRequestTicketList

61. DeleteDistrictCommand.cs
-------------------------------------------
- Delete District Where Id = @DistrictId

62. DeleteDynamicDefinedTableCellValuesCommand.cs
-------------------------------------------
- dbo.DeleteDynamicDefinedTableCellValues

63. DeleteDynamicDefinedTableRowCommand.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

64. DeleteImportCustomerRawScheduledTask.cs
-------------------------------------------
- dbo.DeleteImportCustomerRaw

65. DeleteProvinceCommand.cs
-------------------------------------------
- Delete Province Where Id = @ProvinceId

66. DeleteRegionCommand.cs
-------------------------------------------
- Delete Region Where Id = @RegionId

67. DeleteRequestTicketFirstTaskAutoNextTaskErrorLogCommand.cs
-------------------------------------------
- dbo.DeleteRequestTicketFirstTaskAutoNextTaskErrorLogs

68. DeleteTemplateCodeCallResultCommand.cs
-------------------------------------------
- Delete TemplateCodeCallResult Where CampaignTemplateCodeId = @CampaignTemplateCodeId And CallResultId = @CallResultId

69. DeleteWardCommand.cs
-------------------------------------------
- Delete Ward Where Id = @WardId

70. DistributeAppointmentCommand.cs
-------------------------------------------
- telesale.lead_GetAssignedAppointmentInfos
- telesale.lead_GetCoupleFieldSales

71. DistributeAppointmentCoupleFieldSalesCommand.cs
-------------------------------------------
- telesale.lead_GetCoupleFieldSales

72. DistributeContactsToAgentBySelectionCommand.cs
-------------------------------------------
- telesale.Prospect_ExecuteAgentDistribution

73. DistributeContactsToTeamByQuantityCommand.cs
-------------------------------------------
- telesale.Prospect_ExecuteTeamDistribution

74. DistributeHotProspectsCommand.cs
-------------------------------------------
- telesale.Prospect_AssignedHotProspects

75. DistributeProspectAssignmentCommand.cs
-------------------------------------------
- telesale.Prospect_ExecuteAgentReDistribution

76. DistributeWithProvinceCommand.cs
-------------------------------------------
- telesale.DistributeContactToAgentWithProvince

77. DoImportAppartmentsCommand.cs
-------------------------------------------
- dbo.DoImportAppartments

78. DoImportB2BCustomersCommand.cs
-------------------------------------------
- dbo.DoImportB2BCustomers

79. DoImportFeeCategoryPartsCommand.cs
-------------------------------------------
- dbo.DoImportFeeCategoryParts

80. DoImportPartsCommand.cs
-------------------------------------------
- dbo.DoImportParts

81. DoImportPartServiceUsedHistoryCommand.cs
-------------------------------------------
- dbo.DoImportPartServiceUsedHistories

82. DoImportSOCommand.cs
-------------------------------------------
- dbo.DoImportSO

83. DoImportSystemsCommand.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

84. EditHotGroupCommand.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

85. ExecuteEvaluateAutoNextTaskConditionQuery.cs
-------------------------------------------
- dbo.ExecuteEvaluateAutoNextTaskCondition

86. ExecuteImportCustomerCommand.cs
-------------------------------------------
- dbo.ImportCustomer_Execute

87. ExecuteImportFWDContractCommand.cs
-------------------------------------------
- fwd.ImportContractRaw_Excute

88. ExportServiceTypeQuery.cs
-------------------------------------------
- dbo.ExportServiceType

89. ExportWorkflowQuery.cs
-------------------------------------------
- dbo.ExportWorkflow

90. FeeCategoryPartListByMonthlyQuery.cs
-------------------------------------------
- dbo.FeeCategoryPartListByMonthly

91. FWDGetCampaignQuery.cs
-------------------------------------------
- fwd.GetContestListAPI

92. GenerateDebitNoteMonthlyPartFeeBatchCommand.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

93. GeolocationScanImportB2BCustomersCommand.cs
-------------------------------------------
- dbo.GeolocationScanImportB2BCustomers

94. GetAgencyHierarchyQuery.cs
-------------------------------------------
- dbo.GetAgencyHierarchy

95. GetAgencyHierarchySummaryQuery.cs
-------------------------------------------
- dbo.GetAgencyHierarchySummary

96. GetAgentCampaignAppointmentQuery.cs
-------------------------------------------
- telesale.lead_GetAgentCampaignAppointments

97. GetAgentCampaignAssignmentSumaryInfoQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

98. GetAgentCampaignListQuery.cs
-------------------------------------------
- GetAgentCampaignList

99. GetAgentContactAssignmentListByCampaignQuery.cs
-------------------------------------------
- AgentContactAssignmentListByCampaign

100. GetAgentDistributionPlanQuery.cs
-------------------------------------------
- telesale.Prospect_GetAgentDistributionPlan

101. GetAgentDoSurveyByServiceTypeQuery.cs
-------------------------------------------
- dbo.GetAgentDoSurveyByServiceType

102. GetAgentTeamAppointmentQuery.cs
-------------------------------------------
- telesale.lead_GetAgentTeamAppointments

103. GetAllBrandQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

104. GetAllDynamicFieldRelatedTaskTypeQuery.cs
-------------------------------------------
- dbo.GetAllDynamicFieldRelatedTaskType

105. GetAllPartMonthlyFeeListQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

106. GetAllPartTemplateQuery.cs
-------------------------------------------
- SELECT DISTINCT TemplateName FROM dbo.PartTemplate

107. GetAllRootPartQuery.cs
-------------------------------------------
- SELECT * FROM dbo.Part WHERE ParentId IS NULL
- SELECT * FROM dbo.Part WHERE ParentId = @ParentId

108. GetAlternativeContentTemplateNotificationChanelSettingQuery.cs
-------------------------------------------
- GetAlternativeContentTemplateNotificationChanelSetting

109. GetAlternativeNotiChannelContentTemplateByNotificationChannelSettingIdQuery.cs
-------------------------------------------
- GetAlternativeNotiChannelContentTemplateByNotificationChannelSettingId

110. GetAnonymousDigitalContactListInCampaignQuery.cs
-------------------------------------------
- dbo.GetAnonymousDigitalContactListInCampaign

111. GetAppointmentByIdQuery.cs
-------------------------------------------
- GetCreateAppointmentInfo

112. GetAppointmentDetailInfoQuery.cs
-------------------------------------------
- telesale.GetAppointmentDetailInfo

113. GetAppointmentExcelImportSummaryQuery.cs
-------------------------------------------
- telesale.ImportAppointmentResult_GetSummaryErrorCodeReport

114. GetAppointmentHistoriesQuery.cs
-------------------------------------------
- telesale.lead_GetAppointmentHistories

115. GetAppointmentImportExcelDetailQuery.cs
-------------------------------------------
- telesale.ImportAppointmentResult_GetResult

116. GetAppointmentSumaryForAgentQuery.cs
-------------------------------------------
- dbo.GetAppointmentSumaryForAgent

117. GetAssignedQuery.cs
-------------------------------------------
- telesale.Prospect_SearchAssignedInTeamBy

118. GetAutoNextTaskInfoByWorkflowAndTaskTypeQuery.cs
-------------------------------------------
- dbo.GetAutoNextTaskInfoByWorkflowAndTaskType

119. GetAvailableOrganizationQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

120. GetBucketQuery.cs
-------------------------------------------
- dbo.GetBucketDashboard

121. GetCallbackDetailsByCallbackID.cs
-------------------------------------------
- SearchCallbackDetails

122. GetCallListByContact.cs
-------------------------------------------
- GetCallListByContact

123. GetCallResultListToAddToTemplateQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

124. GetCallResultSumaryQuery.cs
-------------------------------------------
- telesale.CallResultSumary

125. GetCampaignDataSummaryForCostEstimationQuery.cs
-------------------------------------------
- dbo.GetCampaignDataSummaryForCostEstimation

126. GetCampaignExecutingTimeInfoListByCampaignIdQuery.cs
-------------------------------------------
- dbo.GetCampaignExecutingTimeInfoListByCampaignId

127. GetCampaignFileFromEntityLinkQuery.cs
-------------------------------------------
- dbo.GetCampaignFileFromEntityLink

128. GetCampaignHistoriesQuery.cs
-------------------------------------------
- dbo.GetEntityHistories
- dbo.GetCampaignHistories

129. GetCampaignInfoByIdQuery.cs
-------------------------------------------
- GetCampaignInfo

130. GetCampaignPushCodeInfoForCostEstimationQuery.cs
-------------------------------------------
- dbo.GetCampaignPushCodeInfoForCostEstimation

131. GetCampaignTemplateCodeListAddToCallResultQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

132. GetCampaignWorkCustomerQuery.cs
-------------------------------------------
- GetCampaignWorkCustomer

133. GetCampaignWorkerQuery.cs
-------------------------------------------
- GetCampaignWorker

134. GetCampaignWorkTicketQuery.cs
-------------------------------------------
- GetCampaignWorkTicket

135. GetChildListByParentContact.cs
-------------------------------------------
- GetChildListByParentContact

136. GetContactAppointmentHistoriesQuery.cs
-------------------------------------------
- telesale.lead_GetContactAppointmentHistories

137. GetContactCallByProspectAssignmentQuery.cs
-------------------------------------------
- dbo.GetCallListByProspectAssignment

138. GetContactDuplicateQuery.cs
-------------------------------------------
- GetContactDuplicate

139. GetContactListByTeamQuery.cs
-------------------------------------------
- GetContactListByTeam

140. GetContactNotesHistoriesQuery.cs
-------------------------------------------
- telesale.Contact_GetContactHistories

141. GetContextHistoriesQuery.cs
-------------------------------------------
- dbo.GetContextHistoriesQuery

142. GetContractListQuery.cs
-------------------------------------------
- telesale.Contact_GetContractList

143. GetCoupleFieldSalesQuery.cs
-------------------------------------------
- telesale.lead_GetCoupleFieldSales

144. GetCustomerAlternativeAddressByListCustomerIdAndRpContactQuery.cs
-------------------------------------------
- GetCustomerAlternativeAddressByListCustomerIdAndRpContact

145. GetCustomerAlternativeAddressQuery.cs
-------------------------------------------
- SearchCustomerAlternativeAddress

146. GetCustomerAppartmentInfoMultiQuery.cs
-------------------------------------------
- dbo.GetCustomerAppartmentInfoMulti

147. GetCustomerByFacebookIdQuery.cs
-------------------------------------------
- GetCustomerByFacebookId

148. GetCustomerByPhoneQuery.cs
-------------------------------------------
- GetCustomerByPhone

149. GetCustomerByRpContactQuery.cs
-------------------------------------------
- GetCustomerByRpContact

150. GetCustomerDigitalContactListInCampaignQuery.cs
-------------------------------------------
- dbo.GetCustomerDigitalContactListInCampaign

151. GetCustomerDuplicateQuery.cs
-------------------------------------------
- dbo.GetCustomerDuplicate

152. GetDataCallResultSumaryQuery.cs
-------------------------------------------
- telesale.DataCallResultSumary

153. GetDataSourceReportQuery.cs
-------------------------------------------
- telesale.DataSourceSumaryReport
- telesale.DataSourceProcessingReport

154. GetDetailContactInfoQuery.cs
-------------------------------------------
- GetDetailContactInfo

155. GetDigitalContactByUserIdListQuery.cs
-------------------------------------------
- dbo.GetDigitalContactByUserIdList

156. GetDigitalContactTypeListByCampaignQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

157. GetDigitalDeliverMessagesHistoryQuery.cs
-------------------------------------------
- dbo.GetDigitalDeliverMessagesHistory

158. GetDigitalDeliverMessagesQuery.cs
-------------------------------------------
- dbo.GetDigitalDeliverMessages

159. GetDigitalMessDetailByProspectAssignmentQuery.cs
-------------------------------------------
- dbo.GetDigitalMessDetailByProspectAssignment

160. GetDigitalResultInCampaignQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

161. GetDistributePlanQuery.cs
-------------------------------------------
- dbo.DistributeWork

162. GetDmoReportQuery.cs
-------------------------------------------
- dbo.GetFieldSaleReport

163. GetDoneRequestTicketImportSessionResultDetailQuery.cs
-------------------------------------------
- dbo.GetDoneRequestTicketImportSessionResultDetail
- dbo.GetDoneRequestTicketImportSessionResultDetail

164. GetDoneRequestTicketListImportSessionQuery.cs
-------------------------------------------
- dbo.GetDoneRequestTicketListImportSession
- dbo.GetDoneRequestTicketListImportSession

165. GetDuplicateContactQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

166. GetDuplicationStagingContactQuery.cs
-------------------------------------------
- Exec ImportContact_Deduplication_GetDuplicationListWithDb @ImportSessionId,5,10

167. GetDynamicDefinedTableCellValueListByDynamicFieldValueIdQuery.cs
-------------------------------------------
- dbo.GetDynamicDefinedTableRowJsonValueList

168. GetDynamicDefinedTableCellValuesQuery.cs
-------------------------------------------
- dbo.GetDynamicDefinedTableCellValues

169. GetDynamicDefinedTableColumnOnFormListByTableSchemaQuery.cs
-------------------------------------------
- dbo.GetDynamicDefinedTableColumnOnFormList

170. GetDynamicFieldByFormIdQuery.cs
-------------------------------------------
- dbo.GetDynamicFieldListByDynamicForm

171. GetDynamicFormByEntityLinkTicketQuery.cs
-------------------------------------------
- dbo.GetDynamicFormByEntityLinkTicketQuery

172. GetDynamicFormValueStatusReportQuery.cs
-------------------------------------------
- telesale.GetDynamicFormValueStatusReport

173. GetEndorsementItemListByValueGroupIdQuery.cs
-------------------------------------------
- dbo.GetEndorsementItemListByValueGroupId

174. GetEndorsementItemsForPaymentQuery.cs
-------------------------------------------
- dbo.GetEndorsementItemsForPayment

175. GetEndorsementSummaryGroupByContestForPaymentQuery.cs
-------------------------------------------
- dbo.GetEndorsementSummaryGroupByContestForPayment

176. GetEntityLinkBusinessSpecificListQuery.cs
-------------------------------------------
- dbo.GetEntityLinkBusinessSpecificList

177. GetEquipmentHistoriesQuery.cs
-------------------------------------------
- dbo.GetEntityHistories
- dbo.GetEquipmentHistories

178. GetFieldSaleLeadListQuery.cs
-------------------------------------------
- telesale.GetFieldSaleLeadList

179. GetFieldSaleQuotaListQuery.cs
-------------------------------------------
- telesale.GetFieldSaleQuotaList

180. GetHotAgentDistributionListQuery.cs
-------------------------------------------
- telesale.GetHotAgentListWithQuotaAndAssigned

181. GetHotListGroupUserQuery.cs
-------------------------------------------
- GetUserInHotListGroup

182. GetHotListMasterDuplicationQuery.cs
-------------------------------------------
- telesale.Contact_GetHotListMasterDuplication

183. GetIdsFromSelectorPathQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

184. GetImportB2BCustomersResultsQuery.cs
-------------------------------------------
- dbo.GetImportB2BCustomersResults

185. GetImportB2BDataStatisticsQuery.cs
-------------------------------------------
- dbo.GetImportB2BDataStatistics

186. GetImportCustomerSummaryQuery.cs
-------------------------------------------
- dbo.ImportCustomer_GetScanDataSummary

187. GetImportCustomerSummaryReportQuery.cs
-------------------------------------------
- dbo.ImportCustomer_GetScanDataSummaryReport

188. GetImportedCustomerByImportSessionIdQuery.cs
-------------------------------------------
- dbo.ImportCustomer_GetResult

189. GetImportedHotListResultQuery.cs
-------------------------------------------
- telesale.GetImportedHotListResults

190. GetImportFWDContractReportQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

191. GetImportFWDContractResultQuery.cs
-------------------------------------------
- fwd.ImportFWDContract_Summary

192. GetImportRequestTicketHistoriesQuery.cs
-------------------------------------------
- dbo.GetImportRequestTicketHistories

193. GetImportSODataStatisticsQuery.cs
-------------------------------------------
- dbo.GetImportSODataStatistics

194. GetImportSOSessionDateTimeRangeQuery.cs
-------------------------------------------
- select min(StartDate) StartDate, max(EndDate) EndDate from dbo.Staging_ImportSO group by ImportSessionId having ImportSessionId = @ImportSessionId

195. GetInfoDashboardQuery.cs
-------------------------------------------
- dbo.Query_Dashboard_KGS

196. GetInternalDuplicationStagingContactQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

197. GetKnowledgeItemByParentIdQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

198. GetKnowledgeItemListQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

199. GetLandingServiceTypeListQuery.cs
-------------------------------------------
- dbo.GetLandingServiceTypeList

200. GetLatestTasksByTaskTypeQuery.cs
-------------------------------------------
- GetLatestTasksByTaskType

201. GetLeadListQuery.cs
-------------------------------------------
- telesale.lead_GetLeadList

202. GetLeadSummaryStatusQuery.cs
-------------------------------------------
- telesale.Lead_GetSummaryStatusByAgent

203. GetLocalizationQuery.cs
-------------------------------------------
- dbo.usp_CreateInserts

204. GetLoopNextQuestionListQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

205. GetMaintenanceDataByValueGroupIdQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

206. GetMonthFeeStatistics.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

207. GetMonthlyPartFeeDebitNoteFileListByBatchQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

208. GetMonthlyPartFeeDebitReminderFileListQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

209. GetMonthlyPartFeeDebitReminderListQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

210. GetMonthlyPartFeeItemDetailListQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

211. GetMonthlyPartFeeItemListByBatchQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

212. GetMonthlyPartFeeItemListQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

213. GetNewMobileNotificationGroupListQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

214. GetNewMobileNotificationQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

215. GetNewSmsListQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

216. GetNotProcessedContactListByAgentQuery.cs
-------------------------------------------
- GetNotProcessedContactListByAgent

217. GetNotProcessedMonthlyPartFeeItemByBatchQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

218. GetOrganizationForTicketQuery.cs
-------------------------------------------
- GetOrganizationForTicket

219. GetPartBookingListQuery.cs
-------------------------------------------
- dbo.GetPartBookingList

220. GetPartMaintenanceDetailQuery.cs
-------------------------------------------
- dbo.GetPartMaintenanceDetail

221. GetPartMonthlyFeeListByCustomerQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

222. GetPathOfPartByIdQuery.cs
-------------------------------------------
- dbo.GetPathOfPart

223. GetPaymentRequestItemListByValueGroupIdQuery.cs
-------------------------------------------
- dbo.GetPaymentRequestItemListByValueGroupId

224. GetPaymentRequestSummaryByValueGroupIdQuery.cs
-------------------------------------------
- dbo.GetPaymentRequestSummaryByValueGroupId

225. GetPlanJobListQuery.cs
-------------------------------------------
- dbo.GetPlanJobList

226. GetProductByCodeQuery.cs
-------------------------------------------
- GetProductByCode

227. GetProductByNameQuery.cs
-------------------------------------------
- GetProductByName

228. GetProductHistoriesQuery.cs
-------------------------------------------
- cdc.GetProductHistories

229. GetProductivityUserQuery.cs
-------------------------------------------
- emccollab.SelfServiceFlow_GetProductivityUser

230. GetProspectByAgentQuery.cs
-------------------------------------------
- GetProspectListInCampaignByAgent

231. GetProspectSummaryInfoQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

232. GetProspectSummaryQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

233. GetQuestionListByAnswerSuiteInCampaignQuery.cs
-------------------------------------------
- GetQuestionListByAnswerSuiteInCampaign

234. GetReportDataResultQuery.cs
-------------------------------------------
- dbo.GetReportDataResult

235. GetReportExpenseItemByContestQuery.cs
-------------------------------------------
- fwd.GetReportExpenseItemByContest

236. GetReportPaymentByContestQuery.cs
-------------------------------------------
- fwd.GetReportPaymentByContest

237. GetReportPaymentListQuery.cs
-------------------------------------------
- fwd.GetReportPayment

238. GetReportSummaryCallback.cs
-------------------------------------------
- dbo.GetReportSummaryCallback

239. GetRequestTicketBoardItemListQuery.cs
-------------------------------------------
- dbo.GetRequestTicketBoardItemList

240. GetRequestTicketBusinessResultListHierarchyQuery.cs
-------------------------------------------
- dbo.GetRequestTicketBusinessResultList

241. GetRequestTicketByCustomerAndServiceTypeQuery.cs
-------------------------------------------
- dbo.GetRequestTicketByCustomerAndServiceType

242. GetRequestTicketByListCustomerIdAndRpContactQuery.cs
-------------------------------------------
- GetRequestTicketByListCustomerIdAndRpContact

243. GetRequestTicketByProspectAssignmentIdQuery.cs
-------------------------------------------
- dbo.GetRequestTicketByProspectAssignmentId

244. GetRequestTicketDynamicModelQuery.cs
-------------------------------------------
- dbo.GetRequestTicketDynamicModels

245. GetRequestTicketFirstTaskAutoNextTaskErrorLisQuery.cs
-------------------------------------------
- dbo.GetRequestTicketFirstTaskAutoNextTaskErrorList

246. GetRequestTicketWebChatListQuery.cs
-------------------------------------------
- dbo.GetRequestTicketWebChatList

247. GetRetrievalDataQuery.cs
-------------------------------------------
- dbo.SearchRetrieval

248. GetScanEndorsementImportSessionRawItemListResultQuery.cs
-------------------------------------------
- fwd.GetScanEndorsementImportSessionRawItemListResult

249. GetScanImportPartServiceUsedHistoryResultQuery.cs
-------------------------------------------
- dbo.ImportPartServiceUsedHistories_GetScanDataSummaryReport

250. GetScanInputDataErrorsQuery.cs
-------------------------------------------
- telesale.Contact_GetErrorsImportHotList

251. GetScanInputStatisticsQuery.cs
-------------------------------------------
- telesale.Contact_GetStatisticsImportHotList

252. GetScanPaymentRequestImportResultQuery.cs
-------------------------------------------
- fwd.GetScanPaymentRequestImportResult

253. GetServiceTypeByDynamicFieldNameQuery.cs
-------------------------------------------
- dbo.GetServiceTypeByDynamicFieldName

254. GetServiceTypeTreeQuery.cs
-------------------------------------------
- dbo.GetServiceTypeTree

255. GetSMSLogListQuery.cs
-------------------------------------------
- GetTopSMSByPhone

256. GetSubContactListByContactListQuery.cs
-------------------------------------------
- GetSubcontactListByContactList

257. GetSumaryPerformanceReportQuery.cs
-------------------------------------------
- dbo.GetSumaryReport

258. GetSurveyCampaignByServiceTypeQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

259. GetSurveyFeedbackListByCustomerQuery.cs
-------------------------------------------
- dbo.GetSurveyFeedbackListByCustomer

260. GetSurveyReportQuery.cs
-------------------------------------------
- GetSurveyReportInbound

261. GetSurveyWithAnswerSuiteReportQuery.cs
-------------------------------------------
- GetSurveyWithAnswerSuiteReport

262. GetTaskBoardItemListQuery.cs
-------------------------------------------
- dbo.GetTaskBoardItemList

263. GetTaskHistoryQuery.cs
-------------------------------------------
- dbo.GetTaskDynamicFormHistories

264. GetTaskListInTaskTypeGroupQuery.cs
-------------------------------------------
- dbo.GetTaskListInTaskTypeGroup

265. GetTaskServiceTypeDefaultOrganizationQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

266. GetTaskSummaryQuery.cs
-------------------------------------------
- dbo.GetTaskSummary

267. GetTaskSummaryQueryCPC.cs
-------------------------------------------
- dbo.GetTaskSummaryCPC

268. GetTaskTypeForTicketQuery.cs
-------------------------------------------
- dbo.GetTaskTypeForTicket

269. GetTaskTypeListByServiceTypeQuery.cs
-------------------------------------------
- dbo.GetTaskTypeListByServiceType

270. GetTeamAssignedSummryListQuery.cs
-------------------------------------------
- telesale.GetTeamAssignedSummryList

271. GetTeamDistributionPlanQuery.cs
-------------------------------------------
- telesale.Prospect_GetTeamDistributionPlan

272. GetTeamLeadListQuery.cs
-------------------------------------------
- telesale.GetTeamLeadList

273. GetTempLockDynamicFormValueReportQuery.cs
-------------------------------------------
- telesale.GetTempLockDynamicFormValueReport

274. GetTicketOwnerSettingFromSurveyFeedbackQuery.cs
-------------------------------------------
- dbo.GetTicketOwnerSettingFromSurveyFeedback

275. GetTicketServiceTypeDefaultOrganizationQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

276. GetTicketWorkQuery.cs
-------------------------------------------
- Campaign_AddWorks_SearchTickets

277. GetTmrPerformanceReportQuery.cs
-------------------------------------------
- dbo.GetTmrPerformanceReport

278. GetTotalSaleReportQuery.cs
-------------------------------------------
- dbo.TotalSaleReport

279. GetUncompletedContactCallResultServiceCallbackQuery.cs
-------------------------------------------
- dbo.GetUncompletedContactCallResultServiceCallbackList

280. GetUserAssignmentByRoutingQuery.cs
-------------------------------------------
- dbo.GetUserAssignmentByRouting

281. GetUserCurrentTicketListByTaskQuery.cs
-------------------------------------------
- dbo.GetUserCurrentTicketListByTask

282. GetUserFuncBusinessPermissionConfigsInOrgQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

283. GetUserTaskAssignmentRoutingListQuery.cs
-------------------------------------------
- dbo.GetUserTaskAssignmentRoutingList

284. GetUserWorkerQuery.cs
-------------------------------------------
- SearchUsersAddToCampaign

285. GetUWCountQuery.cs
-------------------------------------------
- emccollab.SelfServiceFlow_UWCount

286. GetValdatePartBookingListQuery.cs
-------------------------------------------
- dbo.GetValdatePartBookingList

287. GetWebChatMessageListQuery.cs
-------------------------------------------
- dbo.GetWebChatMessageList

288. GetWorkflowTaskTypeGroupListQuery.cs
-------------------------------------------
- dbo.GetWorkflowTaskTypeGroupList

289. IDynamicDefinedTableUtility.cs
-------------------------------------------
- dbo.CreateOwnDbTableForDynamicDefinedTableSchema

290. ImportAppointmentQuery.cs
-------------------------------------------
- telesale.ImportAppointmentFromExcel

291. ImportCustomerToCampaignCommand.cs
-------------------------------------------
- dbo.ImportCustomerToCampaign

292. ImportMappedRecordsFromFileCommand.cs
-------------------------------------------
- telesale.Contact_ScanHotListImportData

293. ImportOnlyDigitalContactCommand.cs
-------------------------------------------
- dbo.ImportOnlyDigitalContactCommand

294. ImportPartDetailComand.cs
-------------------------------------------
- dbo.ImportPartDetail

295. ImportPartErrorComand.cs
-------------------------------------------
- dbo.ImportPartError

296. ImportPartV2Command.cs
-------------------------------------------
- dbo.ImportPartV2

297. ImportProduct_ScanDataCommand.cs
-------------------------------------------
- dbo.ImportProduct_ScanData

298. ImportRetrievalCommand.cs
-------------------------------------------
- dbo.ImportRetrieval

299. ImportServiceTypeCommandCommand.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

300. ImportStagingPartServiceUsedHistoryCommand.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

301. ImportWorkflowCommand.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

302. InboundReportServices.cs
-------------------------------------------
- dbo.GetTicketReportByServiceType
- dbo.GetTicketReportByOwner
- dbo.RequestTicketDetailWithDynamicFieldInfoReport
- dbo.RequestTicketDetailWithDynamicFieldInfoReportDataTable
- dbo.RequestTicketDetailWithoutDynamicFieldInfoReportDataTable
- dbo.GetSLATongHopReport
- dbo.GetSLATraSoatReport
- dbo.GetSLAChiTietReport
- dbo.OverProcessedRequestTicketSLAReport
- dbo.GetBaoCaoNangSuatLaoDong02Report
- dbo.GetSummaryTicketByServiceTypeAndWeekInYear

303. LandingGetTicketStatusDetailQuery.cs
-------------------------------------------
- dbo.GetTicketStatusDetail

304. LinkTicketTaskPartByNameCommand.cs
-------------------------------------------
- dbo.LinkTicketTaskPartByName

305. LogCustomerImportCommand.cs
-------------------------------------------
- dbo.ImportCustomer_Log

306. MergeCustomerAndUpdateLinkDigitalContactCommand.cs
-------------------------------------------
- dbo.MergeCustomerAndUpdateLinkDigitalContact

307. NotificationCaseScheduledTask.cs
-------------------------------------------
- dbo.RemoveDuplicationNotification

308. NotifyMonthlyPartFeeBatchCommand.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

309. NotifyToCustomersCommand.cs
-------------------------------------------
- dbo.CreateMobileNotificationFromProspectAssignmentList
- dbo.GetDataSetFromProspectAssignmentKey

310. OverwiteCustomerInfoCommand.cs
-------------------------------------------
- dbo.ImportCustomer_OverwriteInfo

311. ParseAutoNextTaskConditionCommand.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

312. ParseAutoNextTaskUserPathSelectorCommand.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

313. ParseMonthlyPartFeeContentCommand.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

314. ParseMonthlyPartFeeReceiptContentCommand.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

315. ParseStaticContentCommand.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

316. ProcessDoneRequestTicketImportSessionCommand.cs
-------------------------------------------
- dbo.ProcessDoneRequestTicketImportSession

317. ProcessEndorsementImportSessionRawItemListCommand.cs
-------------------------------------------
- fwd.ProcessEndorsementImportSessionRawItemList

318. ProcessImportMassSessionCustomerVersioningCommand.cs
-------------------------------------------
- dbo.ProcessImportMassSessionCustomerVersioning

319. ProcessImportOrdersCommand.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

320. ProcessImportStagingCustomerCommand.cs
-------------------------------------------
- dbo.ProcessImportStagingCustomer

321. ProcessPaymentRequestImportCommand.cs
-------------------------------------------
- fwd.ProcessPaymentRequestImport

322. ProcessStagingECommerceCustomersCommand.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

323. ProspectAssignmentService.cs
-------------------------------------------
- telesale.GetDistributedProspectSummary

324. QuickScanImportDataCommand.cs
-------------------------------------------
- dbo.ImportCustomer_QuickScanData

325. ReallocatePartCustomerCommand.cs
-------------------------------------------
- dbo.ReallocatePartCustomer

326. ReallocateRequestTicketCustomerCommand.cs
-------------------------------------------
- dbo.ReallocatedRequestTicketCustomer

327. ReassignCommand.cs
-------------------------------------------
- telesale.Prospect_MoveToOtherTeam

328. ReCalculateDynamicFormValueListCommand.cs
-------------------------------------------
- dbo.GetListOfTicketDynamicFormValueId

329. RegainTicketAssignmentCommand.cs
-------------------------------------------
- RegainCampaignAssignment

330. RemoveExpenseItemListFromEndorsementCommand.cs
-------------------------------------------
- dbo.RemoveExpenseItemListFromEndorsement

331. RemoveExpenseItemsFromPaymentRequestCommand.cs
-------------------------------------------
- dbo.RemoveExpenseItemsFromPaymentRequest

332. RemoveTeamFromCampaignCommand.cs
-------------------------------------------
- RemoveTeamFromCampaign

333. RemoveUserInHotGroupCommand.cs
-------------------------------------------
- DELETE telesale.HotListGroupUser WHERE UserId = @UserId AND HotListGroupId = @GroupId

334. ReportPartStatusQuery.cs
-------------------------------------------
- dbo.PartReportStatus

335. ReportPreprocessDataScheduledTask.cs
-------------------------------------------
- rpt.ReportPreprocessData

336. ReportServices.cs
-------------------------------------------
- dbo.RequestTicketSummaryWithGroupbyReport
- dbo.GetRequestTicketSummaryByTime
- GetF3ReportData
- SourceChanelReport
- BehaviorsReport
- GetRequestTicketSumaryItems
- GetTopComplainedProductQuantity
- GetTopComplainedProductQuantitySamFactoryAndExpired
- GetExchangeDataReportItems
- GetRetrievalReportItems
- GetLeadTimeReportItems
- dbo.GetReportDataProcessStatus
- dbo.GetPredefinedListReportItems
- dbo.RequestTicketSummaryByOwnerReport
- dbo.RequestTicketSummaryByServiceTypeReport
- dbo.RequestTicketReport
- dbo.RequestTicketOwnershipSummaryReport
- dbo.AgentSurveyRatingSummaryReport
- dbo.RequestTicketProcessStatusReport
- dbo.RequestTicketSLADetailReport

337. RequestTicketCreatedEvent.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

338. RequestTicketServices.cs
-------------------------------------------
- dbo.CheckEditRequestTicketPermission
- dbo.GetRequestTicketInteractionHistories
- dbo.GetEntityHistories
- dbo.GetRequestTicketHistories
- dbo.GetProductExchangeHistories
- dbo.GetProductExchangeHistoryDetails
- dbo.GetProductTicketHistories
- dbo.GetProductTicketHistoryDetails
- dbo.SearchRequestTicket
- dbo.ExportRequestTicket
- dbo.ExportRequestTicketTaskTypes
- dbo.ExportRequestTicketRelatedProducts
- dbo.ExportTicketProductExchanges
- dbo.ExportSearchRequestTicketResult
- dbo.ExportSearchRequestTicketDynamicResult
- dbo.ExportSearchRequestTicketDynamicResultWithoutPivot
- dbo.ExportSearchTaskDynamicResult
- dbo.GetCustomerRequestTicketList

339. RetrieveCustomerCommand.cs
-------------------------------------------
- dbo.ImportCustomer_Retrieve

340. SalesSupportDistributeAppointmentsCommand.cs
-------------------------------------------
- telesale.SalesSupportDistributeAppointments

341. SaveAlternativeNotiChannelContentTemplateOrdersCommand.cs
-------------------------------------------
- dbo.SaveAlternativeNotiChannelContentTemplateOrder

342. SaveBusinessPermissionDeletedInfoListCommand.cs
-------------------------------------------
- dbo.SaveBusinessPermissionDeletedInfoList

343. SCADAReportServices.cs
-------------------------------------------
- dbo.GetTerminalOperationReport
- dbo.GetPartOperationSummaryByPCReport
- dbo.GetDisconnectedPartsReport

344. ScanDoneRequestTicketImportSessionCommand.cs
-------------------------------------------
- dbo.ScanDoneRequestTicketImportSession

345. ScanDoneRequestTicketImportSessionResultQuery.cs
-------------------------------------------
- dbo.ScanDoneRequestTicketImportSessionResult

346. ScanEndorsementImportSessionRawItemListCommand.cs
-------------------------------------------
- fwd.ScanEndorsementImportSessionRawItemList

347. ScanImportAppartmentsCommand.cs
-------------------------------------------
- dbo.ScanImportAppartments

348. ScanImportB2BClassificationsCommand.cs
-------------------------------------------
- dbo.ScanImportB2BClassifications

349. ScanImportB2BCustomersCommand.cs
-------------------------------------------
- dbo.ScanImportB2BCustomers

350. ScanImportDataCommand.cs
-------------------------------------------
- dbo.ImportCustomer_ScanData

351. ScanImportFeeCategoryPartsCommand.cs
-------------------------------------------
- dbo.ScanImportFeeCategoryParts

352. ScanImportFWDContractCommand.cs
-------------------------------------------
- fwd.ScanImportContractRaw

353. ScanImportPartsCommand.cs
-------------------------------------------
- dbo.ScanImportParts

354. ScanImportPartServiceUsedHistoryCommand.cs
-------------------------------------------
- dbo.ScanImportPartServiceUsedHistory

355. ScanImportSOCommand.cs
-------------------------------------------
- dbo.ScanImportSO

356. ScanImportSODistributorCommand.cs
-------------------------------------------
- dbo.ScanImportSODistributor

357. ScanImportSOProductCommand.cs
-------------------------------------------
- dbo.ScanImportSOProduct

358. ScanImportSORequestTicketCommand.cs
-------------------------------------------
- dbo.ScanImportSORequestTicket

359. ScanImportSystemsCommand.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

360. ScanInputDataAndPreprocessCommand.cs
-------------------------------------------
- VNM_ImportContact_Deduplication_ScanSourceDataAndPreprocess

361. ScanPaymentRequestImportCommand.cs
-------------------------------------------
- fwd.ScanPaymentRequestImport

362. ScanReferenceObjectImportOrdersCommand.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

363. ScanStagingECommerceCustomersGeolocationCommand.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

364. SearchAgencyWithoutTicketsQuery.cs
-------------------------------------------
- dbo.SearchAgencyWithoutTickets

365. SearchAgentCampaignTicketAssginmentListQuery.cs
-------------------------------------------
- SearchAgentCampaignTicketAssginmentList

366. SearchAndAddRequestTicketToCampaignCommand.cs
-------------------------------------------
- dbo.SearchAndAddRequestTicketToCampaign

367. SearchAnonymousDigitalContactListToAddToCampaignQuery.cs
-------------------------------------------
- dbo.DigitalContact_SearchAnonymousDigitalContactToAddToCampaign

368. SearchAppartmentPartListQuery.cs
-------------------------------------------
- dbo.SearchParts

369. SearchAssignedCampaignListQuery.cs
-------------------------------------------
- telesale.SearchAssignedCampaignList

370. SearchAutoCompleteContentTemplateQuery.cs
-------------------------------------------
- SearchAutoCompleteContentTemplate

371. SearchBehaviorQuery.cs
-------------------------------------------
- dbo.SearchBehaviors

372. SearchCallbackDetailsQuery.cs
-------------------------------------------
- SearchCallbackDetails

373. SearchCallbackListQuery.cs
-------------------------------------------
- SearchCallbackList

374. SearchCallResultsQuery.cs
-------------------------------------------
- telesale.SearchCallResults

375. SearchCampaignByQuery.cs
-------------------------------------------
- SearchCampaign

376. SearchCampaignByTeamLeadQuery.cs
-------------------------------------------
- telesale.SearchTeamLeadCampaignList

377. SearchCampaignDropdownListQuery.cs
-------------------------------------------
- dbo.GetCampaignDropdownList

378. SearchChannelQuery.cs
-------------------------------------------
- dbo.SearchChannels

379. SearchClassificationQuery.cs
-------------------------------------------
- dbo.SearchClassifications

380. SearchContactAddToCampaignQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

381. SearchContactAssignmentListByCampaignQuery.cs
-------------------------------------------
- SearchContactAssignmentListByCampaign

382. SearchContactBySaleSupportAndDistributeAgentQuery.cs
-------------------------------------------
- telesale.SearchContactBySaleSupportAndDistributeAgent

383. SearchContactBySaleSupportAndDistributeCustomerQuery.cs
-------------------------------------------
- telesale.SearchContactBySaleSupportAndDistributeCustomer

384. SearchContactDataCallingQuery.cs
-------------------------------------------
- SearchContactDataCalling

385. SearchContactInfoQuery.cs
-------------------------------------------
- dbo.SearchContact

386. SearchContactListQuery.cs
-------------------------------------------
- telesale.Contact_SearchContactList

387. SearchContactListToAddToCampaignQuery.cs
-------------------------------------------
- dbo.Customer_SearchCustomerToAddToCampaign

388. SearchCustomerAlternativeAddressQuery.cs
-------------------------------------------
- GetCustomerAlternativeAddress

389. SearchCustomerBySaleSupportQuery.cs
-------------------------------------------
- dbo.SearchCustomerForRegainBySaleSupport
- dbo.SearchCustomerAndRegainBySaleSupport

390. SearchCustomersQuery.cs
-------------------------------------------
- dbo.SearchCustomers

391. SearchEndorsementForPaymentQuery.cs
-------------------------------------------
- dbo.SearchEndorsementForPayment

392. SearchEventContactListQuery.cs
-------------------------------------------
- SearchEventContactList

393. SearchFieldSaleAppointmentListQuery.cs
-------------------------------------------
- telesale.SearchFieldSaleAppointmentList

394. SearchGatewayQuery.cs
-------------------------------------------
- dbo.SearchGateways

395. SearchInboundCustomerAlternativeAddressesQuery.cs
-------------------------------------------
- SearchInboundCustomerAlternativeAddresses

396. SearchInboundCustomersQuery.cs
-------------------------------------------
- SearchInboundCustomers

397. SearchInboundTicketsQuery.cs
-------------------------------------------
- SearchInboundTickets

398. SearchKnowledgeItemQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

399. SearchListPolicyQuery.cs
-------------------------------------------
- fwd.SearchListPolicy

400. SearchMarktingContactListQuery.cs
-------------------------------------------
- cia.SearchMarktingContactList

401. SearchPartDataSetQuery.cs
-------------------------------------------
- dbo.SearchParts

402. SearchPartMaintenanceQuery.cs
-------------------------------------------
- dbo.SearchPartMaintenance

403. SearchPartMaintenanceWithRepairQuery.cs
-------------------------------------------
- dbo.SearchPartMaintenanceWithRepair

404. SearchPartMonthlyFeeListQuery.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

405. SearchPartQuery.cs
-------------------------------------------
- dbo.SearchParts

406. SearchPartServiceUsedHistoryListQuery.cs
-------------------------------------------
- dbo.GetPartServiceUsedHistoryList

407. SearchProductListQuery.cs
-------------------------------------------
- SearchProductList

408. SearchProspectListInCampaignQuery.cs
-------------------------------------------
- GetProspectListInCampaignByManager

409. SearchRequestTicketNABQuery.cs
-------------------------------------------
- dbo.SearchRequestTicketNAB

410. SearchRequestTicketQuery.cs
-------------------------------------------
- SearchRequestTicket

411. SearchSaleSupportCampaignListQuery.cs
-------------------------------------------
- telesale.SaleSupport_SearchCampaignList

412. SearchServiceCategoryQuery.cs
-------------------------------------------
- SearchServiceCategory

413. SearchServiceCategoryWithServiceTypeQuery.cs
-------------------------------------------
- SearchServiceCategoryWithServiceType
- SearchServiceCategoryRelatedWithServiceType

414. SearchServiceTypeByTextQuery.cs
-------------------------------------------
- dbo.SearchServiceTypeByText

415. SearchServiceTypeQuery.cs
-------------------------------------------
- SearchServiceType

416. SearchTicketAssignmentListQuery.cs
-------------------------------------------
- SearchTicketAssignmentList

417. SearchToDistributeByTeamLeadQuery.cs
-------------------------------------------
- dbo.SearchToDistributeByTeamLead

418. SearchWebChatListQuery.cs
-------------------------------------------
- emccollab.SearchWebChatList

419. SendWelcomEmailCommand.cs
-------------------------------------------
- dbo.GetDataSetFromProspectAssignmentKey

420. SetAssignedConditionCountCommand.cs
-------------------------------------------
- UPDATE dbo.Task SET AssignedConditionCount = @AssignedConditionCount WHERE Id = @TaskId

421. SetInactiveContactCommand.cs
-------------------------------------------
- UPDATE Contact Set Inactive = @Inactive WHERE Id = @ContactId

422. SyncMissedDynamicFormValueAndDynamicDefinedTableFieldValuesCommand.cs
-------------------------------------------
- dbo.SyncMissedDynamicFormValueAndDynamicDefinedTableFieldValues

423. SyncOwnDbTableColumnForDynamicDefinedTableSchemaCommand.cs
-------------------------------------------
- dbo.SyncOwnDbTableColumnForDynamicDefinedTableSchema

424. UnassignCommand.cs
-------------------------------------------
- telesale.Prospect_ReturnToTeamBucket

425. UpdateDynamicFieldFreezeValueByEndorsementCommand.cs
-------------------------------------------
- fwd.UpdateDynamicFieldFreezeValueByEndorsement

426. UpdateEntityLinkByLinkedTicketColumnCommand.cs
-------------------------------------------
- dbo.UpdateEntityLinkByLinkedTicketColumn

427. UpdateImportCustomerCodeCommand.cs
-------------------------------------------
- dbo.UpdateImportedVLCode

428. UpdateImportCustomerRawCommand.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

429. UpdateImportDigitalContactCommand.cs
-------------------------------------------
- dbo.UpdateImportDigitalContact

430. UpdateLeadUserDefinedFormatCodeCommand.cs
-------------------------------------------
- telesale.UpdateLeadUserDefinedFormatCode

431. UpdateOrderStatusesCommand.cs
-------------------------------------------
- Cannot extract - variable or complex assignment

432. UpdateProspectAssignmentResultCommand.cs
-------------------------------------------
- dbo.UpdateProspectAssignmentResult

433. UpdateProspectStatusByPaListCommand.cs
-------------------------------------------
- dbo.RollbackDistribute

434. UpdateResultContactCallCommand.cs
-------------------------------------------
- Update ContactCall Set CallResult = @CallResult, CallReason = @CallReason, ProductDiscussed = @ProductDiscussed, CallStatus = @CallStatus Where Id = @Id

435. VenusCorpReportServices.cs
-------------------------------------------
- dbo.ExportRequestTicket_LiftCheckList; dbo.ExportRequestTicket_ElectricalCabinetCheckList; dbo.ExportRequestTicketList_ElectricalGeneratorCheckList; dbo.ExportMaintainListDetail; dbo.ExportChecklistByCategory; dbo.ExportDetailChecklistByCategory; dbo.ExportMonthlyFeeList; dbo.GetMonthlyFeePaymentReport; dbo.GetMonthlyFeePaidByTimeReport; dbo.SearchScanErrorLog

III. THỐNG KÊ TỔNG QUAN:
================================================================
- Tổng số folder đã tìm kiếm: 518
- Tổng số file .cs sử dụng stored procedure: 435
- Tổng số stored procedure khác nhau: 375
- Danh sách tất cả stored procedures duy nhất:
   - [telesale].[CreateSurveyFeedback]
   - AgentContactAssignmentListByCampaign
   - BehaviorsReport
   - Campaign_AddWorks_AddTickets
   - Campaign_AddWorks_SearchTickets
   - cdc.GetProductHistories
   - CheckDuplicatedPhone
   - cia.ApproveEventContactList
   - cia.SearchMarktingContactList
   - dbo.AddCustomerIntoCampaignByDynamicFieldDefinitionName
   - dbo.AddDigitalContactsToCampaign
   - dbo.AddExpenseItemsToPaymentRequest
   - dbo.AgentSurveyRatingSummaryReport
   - dbo.BatchCreateRequestTicketList
   - dbo.ChangeTaskOwnedByOrganizationIdBatch
   - dbo.ChangeTicketOwnedByOrganizationIdBatch
   - dbo.CheckDynamicFieldValueDuplicate
   - dbo.CheckDynamicTableValid
   - dbo.CheckEditRequestTicketPermission
   - dbo.CleanUpAndBackUpNotification
   - dbo.CloseAllTodoTaskStatus
   - dbo.CloseNotificationCases
   - dbo.CloseTask
   - dbo.CreateEditContentTemplateListInTaskType
   - dbo.CreateEditDynamicDefinedTableCellValueList
   - dbo.CreateEditDynamicDefinedTableRowJsonValueList
   - dbo.CreateEditTaskTypeListInWorkflow
   - dbo.CreateLogLinkDigitalContact
   - dbo.CreateOwnDbTableForDynamicDefinedTableSchema
   - dbo.CreateRootPart
   - dbo.Customer_SearchCustomerToAddToCampaign
   - dbo.DedupBackendAndTempCustomers
   - dbo.DedupSignleBackendAndTempCustomers
   - dbo.DigitalCampaign_AddCustomerDigitalContactToCampaign
   - dbo.DigitalContact_AddAnonymousDigitalContactToCampaign
   - dbo.DigitalContact_SearchAnonymousDigitalContactToAddToCampaign
   - dbo.DistributeWork
   - dbo.DoImportAppartments
   - dbo.DoImportB2BCustomers
   - dbo.DoImportFeeCategoryParts
   - dbo.DoImportParts
   - dbo.DoImportPartServiceUsedHistories
   - dbo.DoImportSO
   - dbo.ExecuteEvaluateAutoNextTaskCondition
   - dbo.ExportRequestTicket
   - dbo.ExportRequestTicketRelatedProducts
   - dbo.ExportRequestTicketTaskTypes
   - dbo.ExportSearchRequestTicketDynamicResult
   - dbo.ExportSearchRequestTicketDynamicResultWithoutPivot
   - dbo.ExportSearchRequestTicketResult
   - dbo.ExportSearchTaskDynamicResult
   - dbo.ExportServiceType
   - dbo.ExportTicketProductExchanges
   - dbo.ExportWorkflow
   - dbo.FeeCategoryPartListByMonthly
   - dbo.GeolocationScanImportB2BCustomers
   - dbo.GetAgencyHierarchy
   - dbo.GetAgencyHierarchySummary
   - dbo.GetAgentDoSurveyByServiceType
   - dbo.GetAllDynamicDefinedTableCellValueListByRowJsonValue
   - dbo.GetAllDynamicFieldRelatedTaskType
   - dbo.GetAllDynamicFieldValueInfoList
   - dbo.GetAnonymousDigitalContactListInCampaign
   - dbo.GetAppointmentSumaryForAgent
   - dbo.GetAutoDialCustomerListInCampaign
   - dbo.GetAutoNextTaskInfoByWorkflowAndTaskType
   - dbo.GetBaoCaoNangSuatLaoDong02Report
   - dbo.GetBucketDashboard
   - dbo.GetCallListByProspectAssignment
   - dbo.GetCampaignDataSummaryForCostEstimation
   - dbo.GetCampaignDropdownList
   - dbo.GetCampaignExecutingTimeInfoListByCampaignId
   - dbo.GetCampaignHistories
   - dbo.GetCampaignPushCodeInfoForCostEstimation
   - dbo.GetContextHistoriesQuery
   - dbo.GetCustomerAppartmentInfoMulti
   - dbo.GetCustomerDigitalContactListInCampaign
   - dbo.GetCustomerDuplicate
   - dbo.GetCustomerRequestTicketByServiceTypeId
   - dbo.GetCustomerRequestTicketList
   - dbo.GetDetailCallTimesReport
   - dbo.GetDigitalContactByUserIdList
   - dbo.GetDigitalDeliverMessages
   - dbo.GetDigitalDeliverMessagesHistory
   - dbo.GetDigitalMessDetailByProspectAssignment
   - dbo.GetDisconnectedPartsReport
   - dbo.GetDoneRequestTicketImportSessionResultDetail
   - dbo.GetDoneRequestTicketListImportSession
   - dbo.GetDynamicDefinedTableCellValues
   - dbo.GetDynamicDefinedTableColumnOnFormList
   - dbo.GetDynamicDefinedTableRowJsonValueList
   - dbo.GetDynamicFieldListByDynamicForm
   - dbo.GetDynamicFormByEntityLinkTicketQuery
   - dbo.GetEndorsementItemListByValueGroupId
   - dbo.GetEndorsementItemsForPayment
   - dbo.GetEndorsementSummaryGroupByContestForPayment
   - dbo.GetEntityHistories
   - dbo.GetEntityLinkBusinessSpecificList
   - dbo.GetEquipmentHistories
   - dbo.GetFieldSaleReport
   - dbo.GetImportB2BCustomersResults
   - dbo.GetImportB2BDataStatistics
   - dbo.GetImportRequestTicketHistories
   - dbo.GetImportSODataStatistics
   - dbo.GetLandingServiceTypeList
   - dbo.GetListOfTicketDynamicFormValueId
   - dbo.GetNumberOfCallByCallResultSummary
   - dbo.GetPartBookingList
   - dbo.GetPartMaintenanceDetail
   - dbo.GetPartOperationSummaryByPCReport
   - dbo.GetPartServiceUsedHistoryList
   - dbo.GetPathOfPart
   - dbo.GetPaymentRequestItemListByValueGroupId
   - dbo.GetPaymentRequestSummaryByValueGroupId
   - dbo.GetPlanJobList
   - dbo.GetPredefinedListReportItems
   - dbo.GetProductExchangeHistories
   - dbo.GetProductExchangeHistoryDetails
   - dbo.GetProductTicketHistories
   - dbo.GetProductTicketHistoryDetails
   - dbo.GetReportDataProcessStatus
   - dbo.GetReportDataResult
   - dbo.GetReportSummaryCallback
   - dbo.GetRequestTicketBoardItemList
   - dbo.GetRequestTicketBusinessResultList
   - dbo.GetRequestTicketByCustomerAndServiceType
   - dbo.GetRequestTicketByProspectAssignmentId
   - dbo.GetRequestTicketDynamicModels
   - dbo.GetRequestTicketHistories
   - dbo.GetRequestTicketInteractionHistories
   - dbo.GetRequestTicketSummaryByTime
   - dbo.GetRequestTicketWebChatList
   - dbo.GetServiceTypeByDynamicFieldName
   - dbo.GetServiceTypeTree
   - dbo.GetSLAChiTietReport
   - dbo.GetSLATongHopReport
   - dbo.GetSLATraSoatReport
   - dbo.GetSumaryReport
   - dbo.GetSummaryTicketByServiceTypeAndWeekInYear
   - dbo.GetSurveyFeedbackListByCustomer
   - dbo.GetTaskBoardItemList
   - dbo.GetTaskDynamicFormHistories
   - dbo.GetTaskListInTaskTypeGroup
   - dbo.GetTaskSummary
   - dbo.GetTaskSummaryCPC
   - dbo.GetTaskTypeForTicket
   - dbo.GetTaskTypeListByServiceType
   - dbo.GetTerminalOperationReport
   - dbo.GetTicketReportByOwner
   - dbo.GetTicketReportByServiceType
   - dbo.GetTicketStatusDetail
   - dbo.GetTmrPerformanceReport
   - dbo.GetUncompletedContactCallResultServiceCallbackList
   - dbo.GetUserAssignmentByRouting
   - dbo.GetUserCurrentTicketListByTask
   - dbo.GetUserTaskAssignmentRoutingList
   - dbo.GetValdatePartBookingList
   - dbo.GetWebChatMessageList
   - dbo.GetWorkflowTaskTypeGroupList
   - dbo.ImportCustomer_Execute
   - dbo.ImportCustomer_GetResult
   - dbo.ImportCustomer_GetScanDataSummary
   - dbo.ImportCustomer_GetScanDataSummaryReport
   - dbo.ImportCustomer_Log
   - dbo.ImportCustomer_OverwriteInfo
   - dbo.ImportCustomer_QuickScanData
   - dbo.ImportCustomer_Retrieve
   - dbo.ImportCustomer_ScanData
   - dbo.ImportCustomerToCampaign
   - dbo.ImportOnlyDigitalContactCommand
   - dbo.ImportPartDetail
   - dbo.ImportPartServiceUsedHistories_GetScanDataSummaryReport
   - dbo.ImportPartV2
   - dbo.ImportProduct_ScanData
   - dbo.ImportRetrieval
   - dbo.LinkTicketTaskPartByName
   - dbo.OverProcessedRequestTicketSLAReport
   - dbo.PartReportStatus
   - dbo.ProcessDoneRequestTicketImportSession
   - dbo.ProcessImportMassSessionCustomerVersioning
   - dbo.ProcessImportStagingCustomer
   - dbo.Query_Dashboard_KGS
   - dbo.ReallocatedRequestTicketCustomer
   - dbo.ReallocatePartCustomer
   - dbo.RemoveDuplicationNotification
   - dbo.RequestTicketDetailWithDynamicFieldInfoReport
   - dbo.RequestTicketDetailWithDynamicFieldInfoReportDataTable
   - dbo.RequestTicketDetailWithoutDynamicFieldInfoReportDataTable
   - dbo.RequestTicketOwnershipSummaryReport
   - dbo.RequestTicketProcessStatusReport
   - dbo.RequestTicketReport
   - dbo.RequestTicketSLADetailReport
   - dbo.RequestTicketSummaryByOwnerReport
   - dbo.RequestTicketSummaryByServiceTypeReport
   - dbo.RequestTicketSummaryWithGroupbyReport
   - dbo.RollbackDistribute
   - dbo.SaveAlternativeNotiChannelContentTemplateOrder
   - dbo.ScanDoneRequestTicketImportSession
   - dbo.ScanDoneRequestTicketImportSessionResult
   - dbo.ScanImportAppartments
   - dbo.ScanImportB2BClassifications
   - dbo.ScanImportB2BCustomers
   - dbo.ScanImportFeeCategoryParts
   - dbo.ScanImportParts
   - dbo.ScanImportPartServiceUsedHistory
   - dbo.ScanImportSO
   - dbo.ScanImportSODistributor
   - dbo.ScanImportSOProduct
   - dbo.ScanImportSORequestTicket
   - dbo.SearchAgencyWithoutTickets
   - dbo.SearchAndAddRequestTicketToCampaign
   - dbo.SearchBehaviors
   - dbo.SearchChannels
   - dbo.SearchClassifications
   - dbo.SearchContact
   - dbo.SearchCustomerAndRegainBySaleSupport
   - dbo.SearchCustomerForRegainBySaleSupport
   - dbo.SearchCustomers
   - dbo.SearchEndorsementForPayment
   - dbo.SearchGateways
   - dbo.SearchPartMaintenance
   - dbo.SearchPartMaintenanceWithRepair
   - dbo.SearchParts
   - dbo.SearchRequestTicket
   - dbo.SearchRequestTicketNAB
   - dbo.SearchRetrieval
   - dbo.SearchServiceTypeByText
   - dbo.SearchToDistributeByTeamLead
   - dbo.SyncMissedDynamicFormValueAndDynamicDefinedTableFieldValues
   - dbo.SyncOwnDbTableColumnForDynamicDefinedTableSchema
   - dbo.TotalSaleReport
   - emccollab.SearchWebChatList
   - emccollab.SelfServiceFlow_GetProductivityUser
   - emccollab.SelfServiceFlow_UWCount
   - Exec ImportContact_Deduplication_GetDuplicationListWithDb @ImportSessionId,5,10
   - fwd.CommitContestExpense
   - fwd.GetContestListAPI
   - fwd.GetReportExpenseItemByContest
   - fwd.GetReportPayment
   - fwd.GetReportPaymentByContest
   - fwd.GetScanEndorsementImportSessionRawItemListResult
   - fwd.GetScanPaymentRequestImportResult
   - fwd.ImportContractRaw_Excute
   - fwd.ImportFWDContract_Summary
   - fwd.ProcessEndorsementImportSessionRawItemList
   - fwd.ProcessPaymentRequestImport
   - fwd.ScanEndorsementImportSessionRawItemList
   - fwd.ScanImportContractRaw
   - fwd.ScanPaymentRequestImport
   - fwd.SearchListPolicy
   - GetAgentCampaignList
   - GetAlternativeContentTemplateNotificationChanelSetting
   - GetAlternativeNotiChannelContentTemplateByNotificationChannelSettingId
   - GetCallListByContact
   - GetCampaignInfo
   - GetCampaignWorkCustomer
   - GetCampaignWorker
   - GetCampaignWorkTicket
   - GetChildListByParentContact
   - GetContactDuplicate
   - GetContactListByTeam
   - GetCreateAppointmentInfo
   - GetCustomerAlternativeAddress
   - GetCustomerAlternativeAddressByListCustomerIdAndRpContact
   - GetCustomerByFacebookId
   - GetCustomerByPhone
   - GetCustomerByRpContact
   - GetDetailContactInfo
   - GetExchangeDataReportItems
   - GetF3ReportData
   - GetLatestTasksByTaskType
   - GetLeadTimeReportItems
   - GetNotProcessedContactListByAgent
   - GetOrganizationForTicket
   - GetProductByCode
   - GetProductByName
   - GetProspectListInCampaignByAgent
   - GetProspectListInCampaignByManager
   - GetQuestionListByAnswerSuiteInCampaign
   - GetRequestTicketByListCustomerIdAndRpContact
   - GetRequestTicketSumaryItems
   - GetRetrievalReportItems
   - GetSubcontactListByContactList
   - GetSurveyReportInbound
   - GetSurveyWithAnswerSuiteReport
   - GetTopComplainedProductQuantity
   - GetTopComplainedProductQuantitySamFactoryAndExpired
   - GetTopSMSByPhone
   - GetUserInHotListGroup
   - RegainCampaignAssignment
   - ResetRootFlowOrders
   - rpt.ReportPreprocessData
   - SearchAgentCampaignTicketAssginmentList
   - SearchAutoCompleteContentTemplate
   - SearchCallbackDetails
   - SearchCallbackList
   - SearchCampaign
   - SearchContactAssignmentListByCampaign
   - SearchContactDataCalling
   - SearchCustomerAlternativeAddress
   - SearchEventContactList
   - SearchInboundCustomerAlternativeAddresses
   - SearchInboundCustomers
   - SearchInboundTickets
   - SearchProductList
   - SearchRequestTicket
   - SearchServiceCategory
   - SearchServiceCategoryRelatedWithServiceType
   - SearchServiceCategoryWithServiceType
   - SearchServiceType
   - SearchTicketAssignmentList
   - SearchUsersAddToCampaign
   - SourceChanelReport
   - telesale.CallResultFunnelSumary
   - telesale.CallResultSumary
   - telesale.Contact_AddContactsToCampaign
   - telesale.Contact_GetContactHistories
   - telesale.Contact_GetContractList
   - telesale.Contact_GetHotListMasterDuplication
   - telesale.Contact_GetStatisticsImportHotList
   - telesale.Contact_ScanHotListImportData
   - telesale.Contact_SearchContactList
   - telesale.DataCallResultFunnelSumary
   - telesale.DataCallResultSumary
   - telesale.DataSourceProcessingReport
   - telesale.DataSourceSumaryReport
   - telesale.DistributeContactToAgentWithProvince
   - telesale.GetAgentCallResultReport
   - telesale.GetAppointmentDetailInfo
   - telesale.GetAppointmentListReport
   - telesale.GetAppointmentTrackingFields
   - telesale.GetAppointmentTrackings
   - telesale.GetCallResultByTMRsReport
   - telesale.GetCallResultByTMRsReport2
   - telesale.GetContactCallListReport
   - telesale.GetDistributedProspectSummary
   - telesale.GetDynamicFormValueStatusReport
   - telesale.GetFieldSaleLeadList
   - telesale.GetFieldSaleQuotaList
   - telesale.GetHotAgentListWithQuotaAndAssigned
   - telesale.GetImportedHotListResults
   - telesale.GetTeamAssignedSummryList
   - telesale.GetTeamLeadList
   - telesale.GetTempLockDynamicFormValueReport
   - telesale.ImportAppointmentResult_GetResult
   - telesale.lead_GetAgentCampaignAppointments
   - telesale.lead_GetAgentTeamAppointments
   - telesale.lead_GetAppointmentHistories
   - telesale.lead_GetAssignedAppointmentInfos
   - telesale.lead_GetContactAppointmentHistories
   - telesale.lead_GetCoupleFieldSales
   - telesale.lead_GetLeadList
   - telesale.Lead_GetSummaryStatusByAgent
   - telesale.NotifyAppointmentList
   - telesale.Prospect_AssignedHotProspects
   - telesale.Prospect_ExecuteAgentDistribution
   - telesale.Prospect_ExecuteAgentReDistribution
   - telesale.Prospect_ExecuteTeamDistribution
   - telesale.Prospect_GetAgentDistributionPlan
   - telesale.Prospect_GetTeamDistributionPlan
   - telesale.Prospect_MoveToOtherTeam
   - telesale.Prospect_ReturnToTeamBucket
   - telesale.Prospect_SearchAssignedInTeamBy
   - telesale.SalesSupportDistributeAppointments
   - telesale.SaleSupport_SearchCampaignList
   - telesale.SearchAppointmentsToDistribute
   - telesale.SearchAssignedCampaignList
   - telesale.SearchCallResults
   - telesale.SearchContactBySaleSupportAndDistributeAgent
   - telesale.SearchContactBySaleSupportAndDistributeCustomer
   - telesale.SearchFieldSaleAppointmentList
   - telesale.SearchTeamLeadCampaignList
   - VNM_ImportContact_Deduplication_AutoProcessConflictParentCommand
   - VNM_ImportContact_Deduplication_CheckMotherDuplicationStatusInternal
   - VNM_ImportContact_Deduplication_ScanSourceDataAndPreprocess
