DANH SÁCH CÁC STORED PROCEDURE CHUYỂN ĐỔI THÀNH CÔNG
================================================================
   
 
 
--- ScanImportSORequestTicketCommand.cs
---- dbo.ScanImportSORequestTicket

--- DeleteRequestTicketFirstTaskAutoNextTaskErrorLogCommand.cs
---- dbo.DeleteRequestTicketFirstTaskAutoNextTaskErrorLogs (CONVERTED TO LINQ)

--- CloseMobileNotificationBatchCommand.cs
---- dbo.CloseMobileNotificationBatch

--- DeleteDynamicDefinedTableCellValuesCommand.cs
---- dbo.DeleteDynamicDefinedTableCellValues
 

--- ScanImportSOProductCommand.cs
---- dbo.ScanImportSOProduct

--- ScanImportSODistributorCommand.cs
---- dbo.ScanImportSODistributor
 
--- UpdateDialingProspectAssignmentCommand.cs
---- dbo.UpdateDialingProspectAssignment

--- GetAppSingletonExecutedTaskQuery.cs
---- dbo.GetAppSingletonExecutedTask

--- AddUserToHotGroupCommand.cs
---- SELECT * FROM telesale.HotListGroupUser WHERE UserId = @UserId AND HotListGroupId = @GroupId
---- INSERT INTO telesale.HotListGroupUser ( Id, HotListGroupId, UserId ) VALUES  ( NEWID(), @GroupId, @UserId)

--- BulkInsertCustomerRawCommand.cs
---- SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'ImportCustomerRaw'

--- DefaultUserAccountCommand.cs
---- UPDATE dbo.aspnet_Membership SET Email = @Email, LoweredEmail = LOWER(@Email) WHERE UserId = @UserId

--- DeleteDistrictCommand.cs
---- Delete District Where Id = @DistrictId

--- DeleteDynamicDefinedTableCellValuesCommand.cs
---- dbo.DeleteDynamicDefinedTableCellValues

--- DeleteTemplateCodeCallResultCommand.cs
---- Delete TemplateCodeCallResult Where CampaignTemplateCodeId = @CampaignTemplateCodeId And CallResultId = @CallResultId

--- GetAllRootPartQuery.cs
---- SELECT * FROM dbo.Part WHERE ParentId IS NULL
---- SELECT * FROM dbo.Part WHERE ParentId = @ParentId

--- GetAutoNextTaskInfoByWorkflowAndTaskTypeQuery.cs
---- dbo.GetAutoNextTaskInfoByWorkflowAndTaskType

--- GetCallbackDetailsByCallbackID.cs
---- SearchCallbackDetails

--- GetCallListByContact.cs
---- GetCallListByContact

--- GetCampaignExecutingTimeInfoListByCampaignIdQuery.cs
---- dbo.GetCampaignExecutingTimeInfoListByCampaignId

--- GetCampaignFileFromEntityLinkQuery.cs
---- dbo.GetCampaignFileFromEntityLink

--- GetCampaignHistoriesQuery.cs
---- dbo.GetCampaignHistories

--- GetDynamicDefinedTableCellValuesQuery.cs
---- dbo.GetDynamicDefinedTableCellValues

--- RemoveUserInHotGroupCommand.cs
---- DELETE telesale.HotListGroupUser WHERE UserId = @UserId AND HotListGroupId = @GroupId

--- SetAssignedConditionCountCommand.cs
---- UPDATE dbo.Task SET AssignedConditionCount = @AssignedConditionCount WHERE Id = @TaskId

--- SetInactiveContactCommand.cs
---- UPDATE Contact Set Inactive = @Inactive WHERE Id = @ContactId

--- UpdateResultContactCallCommand.cs
---- Update ContactCall Set CallResult = @CallResult, CallReason = @CallReason, ProductDiscussed = @ProductDiscussed, CallStatus = @CallStatus Where Id = @Id

