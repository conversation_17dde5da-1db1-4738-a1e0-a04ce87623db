﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System;
using System.Data;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Outbound.ContactCall.Commands
{
    public class UpdateResultContactCallCommand : CommandBase
    {
        public Guid Id
        {
            get;
            set;
        }

        public Guid CallResult
        {
            get;
            set;
        }

        public Guid CallReason
        {
            get;
            set;
        }

        public Guid? ProductDiscussed
        {
            get;
            set;
        }

        public string CallStatus
        {
            get;
            set;
        }
    }

    public class UpdateResultContactCallCommandHandler : CommandHandlerBase<UpdateResultContactCallCommand>
    {
        public UpdateResultContactCallCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(UpdateResultContactCallCommand command)
        {
            // Convert SQL to LINQ:
            // Original SQL: Update ContactCall Set CallResult = @CallResult, CallReason = @CallReason,
            //               ProductDiscussed = @ProductDiscussed, CallStatus = @CallStatus Where Id = @Id

            var contactCall = await EntitySet.GetAsync<ContactCallEntity>(command.Id);

            if (contactCall != null)
            {
                contactCall.CallResult = command.CallResult;
                contactCall.CallReason = command.CallReason;
                contactCall.ProductDiscussed = command.ProductDiscussed;
                contactCall.CallStatus = command.CallStatus;
                await Repository.SaveAsync(contactCall);
            }

            await EntitySet.ExecuteNonQueryAsync(cmd);
        }
    }
}