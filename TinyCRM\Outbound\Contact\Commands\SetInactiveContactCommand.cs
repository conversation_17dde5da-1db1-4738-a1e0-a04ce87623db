﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System;
using System.Data;
using System.Threading.Tasks;
using Webaby;
using Webaby.Data;
using Webaby.Localization;

namespace TinyCRM.Outbound.Contact.Commands
{
    public class SetInactiveContactCommand : CommandBase
    {
        public Guid Id
        {
            get;
            set;
        }

        public bool Inactive
        {
            get;
            set;
        }
    }

    internal class SetInactiveContactCommandHandler : CommandHandlerBase<SetInactiveContactCommand>
    {
        public SetInactiveContactCommandHandler(IServiceProvider serviceProvider) : base(serviceProvider) { }

        public override async Task ExecuteAsync(SetInactiveContactCommand command)
        {
            // Convert SQL to LINQ:
            // Original SQL: UPDATE Contact Set Inactive = @Inactive WHERE Id = @ContactId

            var contact = await EntitySet.GetAsync<ContactEntity>(command.Id);

            if (contact != null)
            {
                contact.Inactive = command.Inactive;
                await Repository.SaveAsync(contact);
            }
        }
    }
}